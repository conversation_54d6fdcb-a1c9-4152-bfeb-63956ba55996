import { useState, useEffect } from "react";
import { useBusinessParameters } from "@/hooks/useBusinessParameters";

export interface RoiValues {
  cleaningMethod: "manual" | "service" | "conventional";
  totalServiceCost: number;
  area: number;
  cleaningPerformance: number;
  monthlyCleanings: number;
  hourlyRate: number;
  numberOfRobots: number;
  maintenanceCost: number;
  leasingOption: string;
  includeDockingStation: boolean;
  robotCoverage: number;
  // New fields for conventional cleaning machine
  conventionalMachineLeasingCost: number;
  conventionalMachineServiceCost: number;
  conventionalMachinePersonnelCost: number;
  conventionalHourlyRate: number;
}

export interface RoiResults {
  manualCleaningCost: number;
  robotLeasingCost: number;
  totalRobotCost: number;
  savings: number;
  cleaningHours: number;
}

// Export for backward compatibility - will be removed once migration is complete
export const LEASING_RATES = {
  "lease-48": 492,
  "lease-60": 405,
};

export const useRoiCalculator = (initialValues?: Partial<RoiValues>) => {
  const { getDefault, getLeasingRates, isLoading } = useBusinessParameters();
  
  // Get dynamic business parameters with fallback to hardcoded values
  const defaultValues = {
    cleaningMethod: initialValues?.cleaningMethod || "manual",
    totalServiceCost: initialValues?.totalServiceCost || getDefault('default_total_service_cost', 1000),
    area: initialValues?.area || getDefault('default_area', 1500),
    cleaningPerformance: initialValues?.cleaningPerformance || getDefault('default_cleaning_performance', 500),
    monthlyCleanings: initialValues?.monthlyCleanings || getDefault('default_monthly_cleanings', 24),
    hourlyRate: initialValues?.hourlyRate || getDefault('hourly_rate_manual', 20),
    numberOfRobots: initialValues?.numberOfRobots || getDefault('default_number_of_robots', 1),
    maintenanceCost: initialValues?.maintenanceCost || getDefault('default_maintenance_cost', 169),
    leasingOption: initialValues?.leasingOption || "lease-48",
    includeDockingStation: initialValues?.includeDockingStation || false,
    robotCoverage: initialValues?.robotCoverage || getDefault('robot_coverage_default', 85),
    conventionalMachineLeasingCost: initialValues?.conventionalMachineLeasingCost || getDefault('conventional_machine_leasing_cost', 179),
    conventionalMachineServiceCost: initialValues?.conventionalMachineServiceCost || getDefault('conventional_machine_service_cost', 120),
    conventionalMachinePersonnelCost: initialValues?.conventionalMachinePersonnelCost || 0,
    conventionalHourlyRate: initialValues?.conventionalHourlyRate || getDefault('hourly_rate_conventional', 23),
  };
  
  const [values, setValues] = useState<RoiValues>(defaultValues);

  const [results, setResults] = useState<RoiResults>({
    manualCleaningCost: 0,
    robotLeasingCost: 0,
    totalRobotCost: 0,
    savings: 0,
    cleaningHours: 0,
  });

  // Update default values when parameters load
  useEffect(() => {
    if (!isLoading) {
      setValues(prev => ({
        ...prev,
        totalServiceCost: getDefault('default_total_service_cost', prev.totalServiceCost),
        area: getDefault('default_area', prev.area),
        cleaningPerformance: getDefault('default_cleaning_performance', prev.cleaningPerformance),
        monthlyCleanings: getDefault('default_monthly_cleanings', prev.monthlyCleanings),
        hourlyRate: getDefault('hourly_rate_manual', prev.hourlyRate),
        numberOfRobots: getDefault('default_number_of_robots', prev.numberOfRobots),
        maintenanceCost: getDefault('default_maintenance_cost', prev.maintenanceCost),
        robotCoverage: getDefault('robot_coverage_default', prev.robotCoverage),
        conventionalMachineLeasingCost: getDefault('conventional_machine_leasing_cost', prev.conventionalMachineLeasingCost),
        conventionalMachineServiceCost: getDefault('conventional_machine_service_cost', prev.conventionalMachineServiceCost),
        conventionalHourlyRate: getDefault('hourly_rate_conventional', prev.conventionalHourlyRate),
      }));
    }
  }, [isLoading, getDefault]);

  useEffect(() => {
    // Get dynamic leasing rates
    const leasingRates = getLeasingRates();
    const dockingStationFee = getDefault('docking_station_fee', 69);
    
    let baseManualCost;
    if (values.cleaningMethod === "service") {
      baseManualCost = values.totalServiceCost;
    } else if (values.cleaningMethod === "manual") {
      const cleaningHoursPerSession =
        values.area / values.cleaningPerformance || 0;
      const cleaningHoursPerMonth =
        cleaningHoursPerSession * values.monthlyCleanings;
      baseManualCost = cleaningHoursPerMonth * values.hourlyRate;
    } else {
      // Conventional cleaning machine
      const cleaningHoursPerSession =
        values.area / values.cleaningPerformance || 0;
      const cleaningHoursPerMonth =
        cleaningHoursPerSession * values.monthlyCleanings;
      const personnelCost = cleaningHoursPerMonth * values.conventionalHourlyRate;
      
      // Calculate total cost including all components
      baseManualCost = personnelCost + 
                      values.conventionalMachineLeasingCost + 
                      values.conventionalMachineServiceCost;
    }

    // Calculate robot leasing cost including docking station if selected
    const baseLeasingRate = leasingRates[values.leasingOption] || leasingRates['lease-48'];
    const dockingStationCost = values.includeDockingStation ? dockingStationFee : 0;
    const totalLeasingRate =
      (baseLeasingRate + dockingStationCost) * values.numberOfRobots;

    // Calculate costs for the uncovered area that needs manual cleaning
    const manualCleaningPercentage = (100 - values.robotCoverage) / 100;
    const manualCleaningCost = baseManualCost * manualCleaningPercentage;

    // Calculate total robot solution cost
    const totalRobotCost =
      totalLeasingRate + values.maintenanceCost + manualCleaningCost;

    setResults({
      manualCleaningCost: baseManualCost,
      robotLeasingCost: totalLeasingRate,
      totalRobotCost,
      savings: Math.max(0, baseManualCost - totalRobotCost),
      cleaningHours: values.cleaningMethod === "service"
        ? 0
        : (values.area / values.cleaningPerformance) * values.monthlyCleanings,
    });
  }, [values, getDefault, getLeasingRates]);

  const updateValue = <K extends keyof RoiValues>(
    key: K,
    value: RoiValues[K]
  ) => {
    setValues((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  return {
    values,
    results,
    updateValue,
    isLoading,
  };
};
