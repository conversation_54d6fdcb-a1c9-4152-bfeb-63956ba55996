import React, { useState, useRef, useEffect } from "react";
import { Slider } from "@/components/ui/slider";
import * as SliderPrimitive from "@radix-ui/react-slider";
import NumberInput from "./NumberInput";

const SliderTest: React.FC = () => {
  const [value, setValue] = useState([50]);
  const [numberInputValue, setNumberInputValue] = useState(500);
  const sliderRef = useRef<HTMLDivElement>(null);

  const handleValueChange = (newValue: number[]) => {
    console.log("Slider value changed:", newValue);
    setValue(newValue);
  };

  const handleNumberInputChange = (newValue: number) => {
    console.log("NumberInput value changed:", newValue);
    setNumberInputValue(newValue);
  };

  useEffect(() => {
    // Check if slider element is properly rendered
    if (sliderRef.current) {
      console.log("Slider element:", sliderRef.current);
      console.log("Slider computed styles:", window.getComputedStyle(sliderRef.current));

      // Check for any overlapping elements
      const rect = sliderRef.current.getBoundingClientRect();
      console.log("Slider bounding rect:", rect);

      // Check if there are any elements on top of the slider
      const elementAtPoint = document.elementFromPoint(rect.left + rect.width / 2, rect.top + rect.height / 2);
      console.log("Element at slider center:", elementAtPoint);
    }
  }, []);

  return (
    <div className="p-8 space-y-8">
      <h2 className="text-xl font-bold">Slider Diagnostic Test</h2>

      <div className="space-y-6">
        {/* Basic Slider Test */}
        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-4">Basic Slider Test</h3>
          <label className="block text-sm font-medium mb-2">
            Test Slider (Current value: {value[0]})
          </label>
          <div className="w-full max-w-md py-4" ref={sliderRef}>
            <Slider
              value={value}
              min={0}
              max={100}
              step={1}
              onValueChange={handleValueChange}
              className="w-full"
              onPointerDown={() => console.log("Pointer down on slider")}
              onPointerUp={() => console.log("Pointer up on slider")}
            />
          </div>
          <div className="mt-2 space-x-2">
            <button
              onClick={() => setValue([25])}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
            >
              Set to 25
            </button>
            <button
              onClick={() => setValue([75])}
              className="px-3 py-1 bg-green-500 text-white rounded text-sm"
            >
              Set to 75
            </button>
          </div>
        </div>

        {/* Native HTML Range Input Test */}
        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-4">Native HTML Range Input Test</h3>
          <label className="block text-sm font-medium mb-2">
            Native Range (Current value: {value[0]})
          </label>
          <input
            type="range"
            min={0}
            max={100}
            step={1}
            value={value[0]}
            onChange={(e) => {
              const newVal = parseInt(e.target.value);
              console.log("Native range change:", newVal);
              setValue([newVal]);
            }}
            className="w-full max-w-md"
          />
        </div>

        {/* NumberInput Component Test */}
        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-4">NumberInput Component Test</h3>
          <NumberInput
            id="test-area"
            label="Test Area Input with Slider"
            value={numberInputValue}
            onChange={handleNumberInputChange}
            min={100}
            max={2000}
            step={50}
            unit="m²"
            placeholder="Enter area"
            showSlider={true}
          />
        </div>

        {/* Debug Information */}
        <div className="border p-4 rounded bg-gray-50">
          <h3 className="font-semibold mb-4">Debug Information</h3>
          <div className="space-y-2 text-sm">
            <p><strong>Basic Slider Value:</strong> {JSON.stringify(value)}</p>
            <p><strong>NumberInput Value:</strong> {numberInputValue}</p>
            <p><strong>User Agent:</strong> {navigator.userAgent}</p>
            <p><strong>Touch Support:</strong> {'ontouchstart' in window ? 'Yes' : 'No'}</p>
            <p><strong>Pointer Events Support:</strong> {'onpointerdown' in window ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SliderTest;
