import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useRoiCalculator } from "@/hooks/useRoiCalculator";
import { useBusinessParameters } from "@/hooks/useBusinessParameters";
import NumberInput from "./NumberInput";
import ResultCard from "./ResultCard";
import LeasingOptions from "./LeasingOptions";
import CalculatorHeader from "./CalculatorHeader";
import { TrendingUp, Coins, Settings2, Bot, Users, Calendar, CalendarSync } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { LEASING_RATES } from "@/hooks/useRoiCalculator";

const TabButton = ({
  isActive,
  icon: Icon,
  label,
  onClick
}: {
  isActive: boolean;
  icon: React.ElementType;
  label: string;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className={`
      relative flex items-center gap-2.5 px-6 py-3.5 
      text-sm font-medium transition-all duration-200
      ${isActive
        ? 'text-primary'
        : 'text-muted-foreground hover:text-primary/80'
      }
    `}
  >
    <Icon className={`h-4 w-4 transition-colors duration-200 
      ${isActive ? 'text-primary' : 'text-muted-foreground/70'}`}
    />
    {label}
    {isActive && (
      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary" />
    )}
  </button>
);

const RoiCalculator = () => {
  const [activeTab, setActiveTab] = useState("calculator");
  const { values, results, updateValue } = useRoiCalculator();
  const { getDefault, uiConstraints } = useBusinessParameters();
  
  // Use hardcoded UI constraints (no longer in database)
  const areaConstraints = uiConstraints.area;
  const cleaningPerfConstraints = uiConstraints.cleaningPerformance;
  const hourlyRateConstraints = uiConstraints.hourlyRate;
  const robotCoverageMin = uiConstraints.robotCoverage.min;
  const robotCoverageMax = uiConstraints.robotCoverage.max;
  const dockingStationFee = getDefault('docking_station_fee', 69);

  return (
    <div className="w-full max-w-5xl mx-auto p-4">
      <CalculatorHeader />

      <div className="mb-8">
        <div className="border-b flex items-center justify-center">
          <TabButton
            isActive={activeTab === "calculator"}
            icon={Coins}
            label="Rechner"
            onClick={() => setActiveTab("calculator")}
          />
          <TabButton
            isActive={activeTab === "results"}
            icon={TrendingUp}
            label="Ergebnisse"
            onClick={() => setActiveTab("results")}
          />
        </div>
      </div>

      {activeTab === "calculator" && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardContent className="pt-6 space-y-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Settings2 className="h-5 w-5 text-robot-600" />
                Reinigungsparameter
              </h2>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-4">Reinigungsmethode</h3>
                <RadioGroup
                  value={values.cleaningMethod}
                  onValueChange={(value) => updateValue("cleaningMethod", value as "manual" | "service" | "conventional")}
                  className="grid grid-cols-1 md:grid-cols-3 gap-4"
                >
                  <label
                    className={`
                      relative flex flex-col items-center gap-2 p-3 sm:p-4
                      rounded-lg border-2 cursor-pointer transition-all
                      ${values.cleaningMethod === "manual"
                        ? 'border-robot-600 bg-robot-50/50 shadow-sm'
                        : 'border-gray-200 hover:border-robot-300 hover:bg-gray-50'
                      }
                    `}
                  >
                    <RadioGroupItem value="manual" className="sr-only" />
                    <div className="h-10 w-10 rounded-full bg-robot-100 flex items-center justify-center">
                      <Users className="h-5 w-5 text-robot-600" />
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-sm sm:text-base">Eigenreinigung</p>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        Internes Reinigungs-<br />personal
                      </p>
                    </div>
                    {values.cleaningMethod === "manual" && (
                      <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-robot-600" />
                    )}
                  </label>

                  <label
                    className={`
                      relative flex flex-col items-center gap-2 p-3 sm:p-4
                      rounded-lg border-2 cursor-pointer transition-all
                      ${values.cleaningMethod === "service"
                        ? 'border-robot-600 bg-robot-50/50 shadow-sm'
                        : 'border-gray-200 hover:border-robot-300 hover:bg-gray-50'
                      }
                    `}
                  >
                    <RadioGroupItem value="service" className="sr-only" />
                    <div className="h-10 w-10 rounded-full bg-robot-100 flex items-center justify-center">
                      <Bot className="h-5 w-5 text-robot-600" />
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-sm sm:text-base">
                        <span className="sm:hidden">Reinigungs-<br />dienstleister</span>
                        <span className="hidden sm:inline">Reinigungs<br />dienstleister</span>
                      </p>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        Externer Anbieter
                      </p>
                    </div>
                    {values.cleaningMethod === "service" && (
                      <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-robot-600" />
                    )}
                  </label>

                  <label
                    className={`
                      relative flex flex-col items-center gap-2 p-3 sm:p-4
                      rounded-lg border-2 cursor-pointer transition-all
                      ${values.cleaningMethod === "conventional"
                        ? 'border-robot-600 bg-robot-50/50 shadow-sm'
                        : 'border-gray-200 hover:border-robot-300 hover:bg-gray-50'
                      }
                    `}
                  >
                    <RadioGroupItem value="conventional" className="sr-only" />
                    <div className="h-10 w-10 rounded-full bg-robot-100 flex items-center justify-center">
                      <Settings2 className="h-5 w-5 text-robot-600" />
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-sm sm:text-base">
                        Konventionelle<br />Maschine
                      </p>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        SC500/SC401
                      </p>
                    </div>
                    {values.cleaningMethod === "conventional" && (
                      <div className="absolute top-2 right-2 h-2 w-2 rounded-full bg-robot-600" />
                    )}
                  </label>
                </RadioGroup>
              </div>

              {values.cleaningMethod === "service" ? (
                <NumberInput
                  id="totalServiceCost"
                  label="Monatliche Servicekosten"
                  value={values.totalServiceCost}
                  onChange={(value) => updateValue("totalServiceCost", value)}
                  min={0}
                  step={100}
                  unit="€"
                  placeholder="Monatliche Servicekosten eingeben"
                />
              ) : values.cleaningMethod === "conventional" ? (
                <>
                  <NumberInput
                    id="area"
                    label="Zu reinigende Fläche"
                    value={values.area}
                    onChange={(value) => updateValue("area", value)}
                    min={areaConstraints.min}
                    max={areaConstraints.max}
                    step={areaConstraints.step}
                    unit="m²"
                    placeholder="Fläche in m² eingeben"
                    showSlider={true}
                  />

                  <NumberInput
                    id="cleaningPerformance"
                    label="Konventionelle Reinigungsleistung"
                    value={values.cleaningPerformance}
                    onChange={(value) => updateValue("cleaningPerformance", value)}
                    min={cleaningPerfConstraints.conventional.min}
                    max={cleaningPerfConstraints.conventional.max}
                    step={cleaningPerfConstraints.conventional.step}
                    unit="m²/h"
                    placeholder="Leistung in m²/h eingeben"
                    showSlider={true}
                  />

                  <NumberInput
                    id="conventionalMachineLeasingCost"
                    label="Monatliche Leasingkosten"
                    value={values.conventionalMachineLeasingCost}
                    onChange={(value) => updateValue("conventionalMachineLeasingCost", value)}
                    min={0}
                    step={10}
                    unit="€"
                    placeholder="Leasingkosten eingeben"
                  />

                  <NumberInput
                    id="conventionalMachineServiceCost"
                    label="Monatliche Servicekosten"
                    value={values.conventionalMachineServiceCost}
                    onChange={(value) => updateValue("conventionalMachineServiceCost", value)}
                    min={0}
                    step={10}
                    unit="€"
                    placeholder="Servicekosten eingeben"
                  />

                  <NumberInput
                    id="conventionalHourlyRate"
                    label="Stundensatz (Reinigungspersonal)"
                    value={values.conventionalHourlyRate}
                    onChange={(value) => updateValue("conventionalHourlyRate", value)}
                    min={hourlyRateConstraints.min}
                    step={hourlyRateConstraints.step}
                    unit="€/h"
                    placeholder="Stundensatz in € eingeben"
                  />

                  <NumberInput
                    id="monthlyCleanings"
                    label="Anzahl monatlicher Reinigungen"
                    value={values.monthlyCleanings}
                    onChange={(value) => updateValue("monthlyCleanings", value)}
                    min={1}
                    step={1}
                    placeholder="Monatliche Reinigungen eingeben"
                    icon={CalendarSync}
                  />
                </>
              ) : (
                <>
                  <NumberInput
                    id="area"
                    label="Zu reinigende Fläche"
                    value={values.area}
                    onChange={(value) => updateValue("area", value)}
                    min={areaConstraints.min}
                    max={areaConstraints.max}
                    step={areaConstraints.step}
                    unit="m²"
                    placeholder="Fläche in m² eingeben"
                    showSlider={true}
                  />

                  <NumberInput
                    id="cleaningPerformance"
                    label="Konventionelle Reinigungsleistung"
                    value={values.cleaningPerformance}
                    onChange={(value) => updateValue("cleaningPerformance", value)}
                    min={cleaningPerfConstraints.manual.min}
                    max={cleaningPerfConstraints.manual.max}
                    step={cleaningPerfConstraints.manual.step}
                    unit="m²/h"
                    placeholder="Leistung in m²/h eingeben"
                    showSlider={true}
                  />

                  <NumberInput
                    id="monthlyCleanings"
                    label="Anzahl monatlicher Reinigungen"
                    value={values.monthlyCleanings}
                    onChange={(value) => updateValue("monthlyCleanings", value)}
                    min={1}
                    step={1}
                    placeholder="Monatliche Reinigungen eingeben"
                    icon={CalendarSync}
                  />

                  <NumberInput
                    id="hourlyRate"
                    label="Stundensatz (Reinigungspersonal)"
                    value={values.hourlyRate}
                    onChange={(value) => updateValue("hourlyRate", value)}
                    min={hourlyRateConstraints.min}
                    step={hourlyRateConstraints.step}
                    unit="€/h"
                    placeholder="Stundensatz in € eingeben"
                  />
                </>
              )}

              <div className="relative mt-8 group cursor-pointer" onClick={() => setActiveTab("results")}>
                <div className="absolute inset-0 bg-gradient-to-r from-robot-100 via-robot-50 to-robot-100 transform group-hover:scale-105 transition-transform duration-200 rounded-lg" />
                <div className="relative p-4 border border-robot-200 rounded-lg bg-white/50 backdrop-blur-sm hover:border-robot-300 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-robot-100 rounded-full">
                        <TrendingUp className="h-5 w-5 text-robot-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">Kostenanalyse anzeigen</h3>
                        <p className="text-sm text-gray-500">Detaillierte Aufschlüsselung und Einsparungen ansehen</p>
                      </div>
                    </div>
                    <div className="pr-2">
                      <div className="text-robot-600 transform group-hover:translate-x-1 transition-transform">
                        →
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 space-y-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Bot className="h-5 w-5 text-robot-600" />
                Roboter-Konfiguration
              </h2>

              <NumberInput
                id="numberOfRobots"
                label="Anzahl der Roboter"
                value={values.numberOfRobots}
                onChange={(value) => updateValue("numberOfRobots", value)}
                min={1}
                step={1}
                placeholder="Anzahl der Roboter eingeben"
                icon={Bot}
              />

              <LeasingOptions
                selectedOption={values.leasingOption}
                onChange={(option) => updateValue("leasingOption", option)}
              />

              <NumberInput
                id="maintenanceCost"
                label="Monatliche Wartungskosten"
                value={values.maintenanceCost}
                onChange={(value) => updateValue("maintenanceCost", value)}
                min={0}
                step={10}
                unit="€"
                placeholder="Wartungskosten eingeben"
              />

              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Dockingstation</label>
                    <p className="text-sm text-muted-foreground">
                      Zusätzlich €{dockingStationFee}/Monat pro Roboter
                    </p>
                  </div>
                  <Switch
                    checked={values.includeDockingStation}
                    onCheckedChange={(checked) => updateValue("includeDockingStation", checked)}
                    className="data-[state=checked]:bg-robot-600"
                  />
                </div>

                <div>
                  <NumberInput
                    id="robotCoverage"
                    label="Roboter-Abdeckungsgrad"
                    value={values.robotCoverage}
                    onChange={(value) => updateValue("robotCoverage", value)}
                    min={robotCoverageMin}
                    max={robotCoverageMax}
                    step={1}
                    unit="%"
                    placeholder="Abdeckungsgrad eingeben"
                    showSlider={true}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Erforderliche manuelle Reinigung:{" "}
                    <span className="text-robot-600 font-medium">
                      {100 - values.robotCoverage}%
                    </span>
                    {" "}der Fläche
                  </p>
                </div>
              </div>

              <div className="pt-4">
                <ResultCard
                  manualCost={results.manualCleaningCost}
                  robotCost={results.totalRobotCost}
                  savings={results.savings}
                  monthlyCleanings={values.monthlyCleanings}
                  cleaningHours={results.cleaningHours}
                  showDetails={false}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === "results" && (
        <div className="w-full mx-auto">
          <ResultCard
            manualCost={results.manualCleaningCost}
            robotCost={results.totalRobotCost}
            savings={results.savings}
            monthlyCleanings={values.monthlyCleanings}
            cleaningHours={results.cleaningHours}
          />

          <div className="mt-6 bg-gray-50 p-6 rounded-lg border border-gray-200">
            <h3 className="text-xl font-semibold mb-4">Detaillierte Aufschlüsselung</h3>

            <div className="space-y-4">
              <div className={`grid grid-cols-1 ${values.cleaningMethod === "manual" || values.cleaningMethod === "conventional"
                ? 'md:grid-cols-2'
                : ''
                } gap-4`}>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-3">Roboter-Reinigung</h4>
                  <ul className="space-y-2">
                    <li className="text-sm flex justify-between items-center">
                      <span className="text-gray-600">Anzahl der Roboter:</span>
                      <span className="font-medium">{values.numberOfRobots}</span>
                    </li>
                    <li className="text-sm flex justify-between items-center">
                      <span className="text-gray-600">Leasing-Option:</span>
                      <span className="font-medium">
                        {values.leasingOption === "lease-48" ? "48 Monate" : "60 Monate"}
                      </span>
                    </li>
                    <li className="text-sm flex justify-between items-center">
                      <span className="text-gray-600">Leasing-Rate:</span>
                      <span className="font-medium">
                        {formatCurrency(results.robotLeasingCost)}
                      </span>
                    </li>
                    <li className="text-sm flex justify-between items-center">
                      <span className="text-gray-600">Wartungskosten:</span>
                      <span className="font-medium">{formatCurrency(values.maintenanceCost)}</span>
                    </li>
                    <li className="text-sm flex justify-between items-center">
                      <span className="text-gray-600">Erforderliche manuelle Reinigungskosten:</span>
                      <span className="font-medium">
                        {formatCurrency(results.manualCleaningCost * ((100 - values.robotCoverage) / 100))}
                      </span>
                    </li>
                    <li className="text-sm flex justify-between items-center font-semibold">
                      <span className="text-gray-600">Monatliche Gesamtkosten:</span>
                      <span>{formatCurrency(results.totalRobotCost)}</span>
                    </li>
                  </ul>
                </div>

                {values.cleaningMethod === "manual" && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Manuelle Reinigung</h4>
                    <ul className="space-y-2">
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Zu reinigende Fläche:</span>
                        <span className="font-medium">{values.area} m²</span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Reinigungsleistung:</span>
                        <span className="font-medium">{values.cleaningPerformance} m²/h</span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Zeit pro Reinigung:</span>
                        <span className="font-medium">
                          {(values.area / values.cleaningPerformance).toFixed(1)} Stunden
                        </span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Monatliche Reinigungen:</span>
                        <span className="font-medium">{values.monthlyCleanings}</span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Stundensatz:</span>
                        <span className="font-medium">{values.hourlyRate} €/h</span>
                      </li>
                      <li className="text-sm flex justify-between items-center font-semibold">
                        <span className="text-gray-600">Monatliche Kosten:</span>
                        <span>{formatCurrency(results.manualCleaningCost)}</span>
                      </li>
                    </ul>
                  </div>
                )}

                {values.cleaningMethod === "conventional" && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Konventionelle Maschine</h4>
                    <ul className="space-y-2">
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Zu reinigende Fläche:</span>
                        <span className="font-medium">{values.area} m²</span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Reinigungsleistung:</span>
                        <span className="font-medium">{values.cleaningPerformance} m²/h</span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Monatliche Leasingkosten:</span>
                        <span className="font-medium">{formatCurrency(values.conventionalMachineLeasingCost)}</span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Monatliche Servicekosten:</span>
                        <span className="font-medium">{formatCurrency(values.conventionalMachineServiceCost)}</span>
                      </li>
                      <li className="text-sm flex justify-between items-center">
                        <span className="text-gray-600">Personalkosten:</span>
                        <span className="font-medium">
                          {formatCurrency(values.conventionalHourlyRate * (values.area / values.cleaningPerformance) * values.monthlyCleanings)}
                        </span>
                      </li>
                      <li className="text-sm flex justify-between items-center font-semibold">
                        <span className="text-gray-600">Monatliche Gesamtkosten:</span>
                        <span>{formatCurrency(results.manualCleaningCost)}</span>
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoiCalculator;
