import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useRoiCalculator } from "@/hooks/useRoiCalculator";
import NumberInput from "./NumberInput";
import ResultCard from "./ResultCard";
import LeasingOptions from "./LeasingOptions";
import CalculatorHeader from "./CalculatorHeader";
import { TrendingUp, Coins, Settings2, Bot, Users, Calendar, CalendarSync } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

const TabButton = ({
  isActive,
  icon: Icon,
  label,
  onClick
}: {
  isActive: boolean;
  icon: React.ElementType;
  label: string;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className={`
      relative flex items-center gap-2.5 px-6 py-3.5 
      text-sm font-medium transition-all duration-200
      ${isActive
        ? 'text-primary'
        : 'text-muted-foreground hover:text-primary/80'
      }
    `}
  >
    <Icon className={`h-4 w-4 transition-colors duration-200 
      ${isActive ? 'text-primary' : 'text-muted-foreground/70'}`}
    />
    {label}
    {isActive && (
      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary" />
    )}
  </button>
);

const RoiCalculator = () => {
  const [activeTab, setActiveTab] = useState("calculator");
  const { values, results, updateValue } = useRoiCalculator();

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <CalculatorHeader />

      <div className="mb-8">
        <div className="border-b flex items-center justify-center">
          <TabButton
            isActive={activeTab === "calculator"}
            icon={Coins}
            label="Calculator"
            onClick={() => setActiveTab("calculator")}
          />
          <TabButton
            isActive={activeTab === "results"}
            icon={TrendingUp}
            label="Results"
            onClick={() => setActiveTab("results")}
          />
        </div>
      </div>

      {activeTab === "calculator" && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardContent className="pt-6 space-y-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Settings2 className="h-5 w-5 text-robot-600" />
                Cleaning Parameters
              </h2>

              <NumberInput
                id="area"
                label="Area to be cleaned"
                value={values.area}
                onChange={(value) => updateValue("area", value)}
                min={100}
                max={10000}
                step={100}
                unit="m²"
                placeholder="Enter area in m²"
                showSlider={true}
              />

              <NumberInput
                id="cleaningPerformance"
                label="Conventional cleaning performance"
                value={values.cleaningPerformance}
                onChange={(value) => updateValue("cleaningPerformance", value)}
                min={50}
                max={500}
                step={10}
                unit="m²/h"
                placeholder="Enter performance in m²/h"
                showSlider={true}
              />

              <NumberInput
                id="monthlyCleanings"
                label="Number of monthly cleanings"
                value={values.monthlyCleanings}
                onChange={(value) => updateValue("monthlyCleanings", value)}
                min={1}
                step={1}
                placeholder="Enter monthly cleanings"
                icon={CalendarSync}
              />

              <NumberInput
                id="hourlyRate"
                label="Hourly billing rate (cleaning staff)"
                value={values.hourlyRate}
                onChange={(value) => updateValue("hourlyRate", value)}
                min={1}
                step={0.5}
                unit="€/h"
                placeholder="Enter hourly rate in €"
              />

              <div className="relative mt-8 group cursor-pointer" onClick={() => setActiveTab("results")}>
                <div className="absolute inset-0 bg-gradient-to-r from-robot-100 via-robot-50 to-robot-100 transform group-hover:scale-105 transition-transform duration-200 rounded-lg" />
                <div className="relative p-4 border border-robot-200 rounded-lg bg-white/50 backdrop-blur-sm hover:border-robot-300 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-robot-100 rounded-full">
                        <TrendingUp className="h-5 w-5 text-robot-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">View Cost Analysis</h3>
                        <p className="text-sm text-gray-500">See detailed breakdown and savings</p>
                      </div>
                    </div>
                    <div className="pr-2">
                      <div className="text-robot-600 transform group-hover:translate-x-1 transition-transform">
                        →
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 space-y-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Bot className="h-5 w-5 text-robot-600" />
                Robot Configuration
              </h2>

              <NumberInput
                id="numberOfRobots"
                label="Number of robots"
                value={values.numberOfRobots}
                onChange={(value) => updateValue("numberOfRobots", value)}
                min={1}
                step={1}
                placeholder="Enter number of robots"
                icon={Bot}
              />

              <LeasingOptions
                selectedOption={values.leasingOption}
                onChange={(option) => updateValue("leasingOption", option)}
              />

              <NumberInput
                id="maintenanceCost"
                label="Monthly maintenance costs (if any)"
                value={values.maintenanceCost}
                onChange={(value) => updateValue("maintenanceCost", value)}
                min={0}
                step={10}
                unit="€"
                placeholder="Enter maintenance costs in €"
              />

              <div className="pt-4">
                <ResultCard
                  manualCost={results.manualCleaningCost}
                  robotCost={results.totalRobotCost}
                  savings={results.savings}
                  monthlyCleanings={values.monthlyCleanings}
                  cleaningHours={results.cleaningHours}
                  showDetails={false}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === "results" && (
        <div className="w-full mx-auto">
          <ResultCard
            manualCost={results.manualCleaningCost}
            robotCost={results.totalRobotCost}
            savings={results.savings}
            monthlyCleanings={values.monthlyCleanings}
            cleaningHours={results.cleaningHours}
          />

          <div className="mt-6 bg-gray-50 p-6 rounded-lg border border-gray-200">
            <h3 className="text-xl font-semibold mb-4">Detailed Breakdown</h3>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Manual Cleaning</h4>
                  <ul className="space-y-2 mt-2">
                    <li className="text-sm flex justify-between">
                      <span>Area to clean:</span>
                      <span className="font-medium">{values.area} m²</span>
                    </li>
                    <li className="text-sm flex justify-between">
                      <span>Cleaning performance:</span>
                      <span className="font-medium">{values.cleaningPerformance} m²/h</span>
                    </li>
                    <li className="text-sm flex justify-between">
                      <span>Time per cleaning:</span>
                      <span className="font-medium">
                        {(values.area / values.cleaningPerformance).toFixed(1)} hours
                      </span>
                    </li>
                    <li className="text-sm flex justify-between">
                      <span>Monthly cleanings:</span>
                      <span className="font-medium">{values.monthlyCleanings}</span>
                    </li>
                    <li className="text-sm flex justify-between">
                      <span>Hourly rate:</span>
                      <span className="font-medium">{values.hourlyRate} €/h</span>
                    </li>
                    <li className="text-sm flex justify-between font-semibold">
                      <span>Monthly cost:</span>
                      <span>{formatCurrency(results.manualCleaningCost)}</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-500">Robot Cleaning</h4>
                  <ul className="space-y-2 mt-2">
                    <li className="text-sm flex justify-between">
                      <span>Number of robots:</span>
                      <span className="font-medium">{values.numberOfRobots}</span>
                    </li>
                    <li className="text-sm flex justify-between">
                      <span>Leasing option:</span>
                      <span className="font-medium">
                        {values.leasingOption === "lease-48" ? "48 months" : "60 months"}
                      </span>
                    </li>
                    <li className="text-sm flex justify-between">
                      <span>Leasing rate:</span>
                      <span className="font-medium">
                        {formatCurrency(results.robotLeasingCost)}
                      </span>
                    </li>
                    <li className="text-sm flex justify-between">
                      <span>Maintenance cost:</span>
                      <span className="font-medium">{formatCurrency(values.maintenanceCost)}</span>
                    </li>
                    <li className="text-sm flex justify-between font-semibold">
                      <span>Monthly cost:</span>
                      <span>{formatCurrency(results.totalRobotCost)}</span>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="pt-2 mt-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">Annual savings:</span>
                  <span className="text-2xl font-bold text-green-600">
                    {formatCurrency(results.savings * 12)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoiCalculator;
