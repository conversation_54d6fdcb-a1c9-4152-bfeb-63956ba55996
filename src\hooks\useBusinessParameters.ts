import { useState, useEffect } from 'react';
import { businessParameterService, UI_CONSTRAINTS, FALLBACK_DEFAULTS } from '@/services/businessParameters';
import type { CalculationDefault } from '@/services/businessParameters';

interface UseBusinessParametersReturn {
  // Numeric parameter getters
  getDefault: (key: string, fallback: number) => number;
  
  // Complex parameter getters
  getLeasingRates: () => Record<string, number>;
  getLeasingDurations: () => Array<{ id: string; months: number; isPopular?: boolean }>;
  
  // UI constraints (hardcoded)
  uiConstraints: typeof UI_CONSTRAINTS;
  
  // Loading state
  isLoading: boolean;
  error: string | null;
  
  // Raw data for admin  
  calculationDefaults: CalculationDefault[];
  
  // Manual refresh function
  refresh: () => Promise<void>;
}

export const useBusinessParameters = (): UseBusinessParametersReturn => {
  const [calculationDefaults, setCalculationDefaults] = useState<CalculationDefault[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load parameters function with graceful error handling
  const loadParameters = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Clear cache to force fresh data
      businessParameterService.clearCache();
      const defaults = await businessParameterService.getCalculationDefaults();
      
      setCalculationDefaults(defaults);
      
      // If no data loaded, it means we're running on fallback values
      if (defaults.length === 0) {
        console.warn('No parameters loaded from database, calculator will use fallback values');
        setError('Database unavailable - using default values');
      }
    } catch (err) {
      console.error('Error loading business parameters:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load parameters';
      setError(`${errorMessage} - calculator will use default values`);
      // Don't prevent the app from working - it will use fallback values
      setCalculationDefaults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Manual refresh function
  const refresh = async () => {
    await loadParameters();
  };

  // Load parameters on mount
  useEffect(() => {
    let isMounted = true;
    
    const loadParametersIfMounted = async () => {
      if (isMounted) {
        await loadParameters();
      }
    };

    loadParametersIfMounted();

    // Subscribe to real-time updates
    const unsubscribe = businessParameterService.subscribeToUpdates(() => {
      if (isMounted) {
        loadParametersIfMounted();
      }
    });

    return () => {
      isMounted = false;
      unsubscribe();
    };
  }, []);

  // Get a numeric parameter value with comprehensive fallback support
  const getDefault = (key: string, fallback: number): number => {
    const param = calculationDefaults.find(p => p.parameter_key === key);
    // Use database value if available, otherwise FALLBACK_DEFAULTS, then provided fallback
    return param?.parameter_value ?? FALLBACK_DEFAULTS[key as keyof typeof FALLBACK_DEFAULTS] ?? fallback;
  };

  // Get leasing rates with robust fallback support
  const getLeasingRates = (): Record<string, number> => {
    const rate48 = calculationDefaults.find(p => p.parameter_key === 'leasing_rate_48_months')?.parameter_value 
                 ?? FALLBACK_DEFAULTS.leasing_rate_48_months;
    const rate60 = calculationDefaults.find(p => p.parameter_key === 'leasing_rate_60_months')?.parameter_value 
                 ?? FALLBACK_DEFAULTS.leasing_rate_60_months;
    
    return {
      "lease-48": rate48,
      "lease-60": rate60
    };
  };

  // Get leasing durations (hardcoded - always the same 2 options)
  const getLeasingDurations = () => {
    return [
      { id: "lease-48", months: 48, isPopular: true },
      { id: "lease-60", months: 60 }
    ];
  };

  return {
    getDefault,
    getLeasingRates,
    getLeasingDurations,
    uiConstraints: UI_CONSTRAINTS,
    isLoading,
    error,
    calculationDefaults,
    refresh
  };
}; 