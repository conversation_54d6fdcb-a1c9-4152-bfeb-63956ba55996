# Admin Panel Implementation for Dynamic Parameter Management

## Overview
Implementation of an admin panel to manage calculation parameters dynamically through Supabase, replacing all hardcoded values while preserving existing calculation logic.

## Hardcoded Parameters Identified

### 1. Leasing Rates (useRoiCalculator.ts)
- 48 months: €492/month
- 60 months: €405/month

### 2. Default Values (useRoiCalculator.ts)
- Total Service Cost: €1000
- Area: 1500 m²
- Cleaning Performance: 500 m²/h
- Monthly Cleanings: 24
- Hourly Rate: €20/h
- Number of Robots: 1
- Maintenance Cost: €169
- Robot Coverage: 85%
- Conventional Machine Leasing Cost: €179
- Conventional Machine Service Cost: €120
- Conventional Hourly Rate: €23/h

### 3. Fixed Costs
- Docking Station Fee: €69/month per robot

### 4. UI Constraints
- Robot Coverage: min 70%, max 95%
- Area: min 100, max 10000
- Cleaning Performance: min 50-500, max 700-1200 (varies by type)

## Implementation Plan

### Phase 1: Codebase Analysis ✅
- [x] Identify all hardcoded parameters
- [x] Map parameter usage across components
- [x] Document parameter types and constraints
- [x] Analyze calculation flow

### Phase 2: Database Schema Design ✅
- [x] Design parameter categories structure
- [x] Create Supabase tables
- [x] Set up indexes and constraints
- [x] Implement audit trail
- [x] Create RLS policies

### Phase 3: Admin Panel Development ✅
- [x] Create admin authentication system
- [x] Build admin layout and navigation
- [x] Implement parameter CRUD interface
- [x] Add parameter validation
- [x] Create smart JSON parameter editor for user-friendly editing
- [ ] Create bulk import/export
- [ ] Add parameter history view

### Phase 4: Integration Layer ✅
- [x] Create parameter service hook
- [x] Implement caching strategy
- [x] Add loading states
- [x] Create fallback mechanism
- [x] Set up real-time subscriptions

### Phase 5: Migration & Testing ✅
- [x] Migrate hardcoded values to database
- [x] Update components to use dynamic parameters
- [ ] Test calculation accuracy
- [ ] Verify performance
- [ ] Create documentation

## Detailed Tasks

### Database Schema ✅
```sql
-- Parameters table
CREATE TABLE parameters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    type TEXT NOT NULL, -- 'number', 'string', 'boolean', 'array'
    category TEXT NOT NULL,
    label TEXT NOT NULL,
    description TEXT,
    unit TEXT,
    min_value NUMERIC,
    max_value NUMERIC,
    default_value JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Parameter history for audit trail
CREATE TABLE parameter_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parameter_id UUID REFERENCES parameters(id),
    old_value JSONB,
    new_value JSONB,
    changed_by UUID,
    changed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Parameter categories
CREATE TABLE parameter_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    display_order INTEGER,
    icon TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Relevant Files

#### New Files Created ✅
- `src/pages/Admin.tsx` - Admin dashboard ✅
- `src/pages/AdminLogin.tsx` - Admin authentication ✅
- `src/components/admin/ParameterManager.tsx` - Parameter CRUD interface ✅
- `src/components/admin/JsonParameterEditor.tsx` - Smart JSON parameter editor ✅
- `src/components/admin/ParameterForm.tsx` - Parameter edit form
- `src/components/admin/ParameterHistory.tsx` - Audit trail viewer
- `src/services/parameters.ts` - Parameter service layer ✅
- `src/hooks/useParameters.ts` - Parameter hook with caching ✅
- `src/hooks/useAdminAuth.ts` - Admin authentication hook ✅
- `src/lib/supabase.ts` - Supabase client configuration ✅
- `supabase/migrations/001_create_parameters_tables.sql` - Database schema ✅
- `supabase/migrations/002_insert_initial_parameters.sql` - Initial data ✅

#### Files Modified ✅
- `src/hooks/useRoiCalculator.ts` - Use dynamic parameters ✅
- `src/components/LeasingOptions.tsx` - Use dynamic leasing rates ✅
- `src/components/RoiCalculator.tsx` - Update parameter constraints ✅
- `src/App.tsx` - Add admin routes ✅
- `package.json` - Add Supabase dependencies ✅

### Parameter Categories
1. **Leasing Options**
   - Leasing rates by duration
   - Available durations

2. **Service Costs**
   - Default service cost
   - Maintenance costs
   - Docking station fee

3. **Performance Metrics**
   - Cleaning performance ranges
   - Robot coverage limits
   - Area constraints

4. **Labor Costs**
   - Hourly rates (manual)
   - Hourly rates (conventional)

5. **Default Values**
   - Initial form values
   - Calculation defaults

### Technical Implementation Details

#### Parameter Service Architecture ✅
```typescript
interface Parameter {
  key: string;
  value: any;
  type: 'number' | 'string' | 'boolean' | 'array';
  category: string;
  metadata: {
    label: string;
    description?: string;
    unit?: string;
    min?: number;
    max?: number;
  };
}

interface ParameterService {
  getAll(): Promise<Parameter[]>;
  getByCategory(category: string): Promise<Parameter[]>;
  getByKey(key: string): Promise<Parameter>;
  update(key: string, value: any): Promise<void>;
  bulkUpdate(updates: Partial<Parameter>[]): Promise<void>;
}
```

#### Caching Strategy ✅
- Use React Query or SWR for caching ✅
- Cache parameters on app load ✅
- Invalidate cache on admin updates ✅
- Implement stale-while-revalidate pattern ✅

#### Real-time Updates ✅
- Subscribe to parameter changes via Supabase ✅
- Update cache when parameters change ✅
- Notify users of parameter updates ✅
- Implement optimistic updates ✅

## Next Steps
1. Apply database migrations to Supabase project
2. Configure Supabase environment variables
3. Test admin authentication
4. Test parameter updates and real-time sync
5. Add bulk import/export functionality
6. Implement parameter history view
7. Create user documentation 