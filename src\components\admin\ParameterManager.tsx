import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useParametersByCategory, useUpdateParameter } from '@/hooks/useParameters';
import { parameterService } from '@/services/parameters';
import { type ParameterCategory, type Parameter } from '@/lib/supabase';
import JsonParameterEditor from './JsonParameterEditor';
import { 
  Settings, 
  DollarSign, 
  Activity, 
  Users, 
  Sliders, 
  Save,
  AlertCircle,
  Edit2,
  X,
  Check
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

const categoryIcons: Record<string, React.ElementType> = {
  'leasing_options': DollarSign,
  'service_costs': DollarSign,
  'performance_metrics': Activity,
  'labor_costs': Users,
  'default_values': Settings,
  'ui_constraints': Sliders,
};

interface EditingParameter {
  key: string;
  value: any;
  isJson: boolean;
}

export default function ParameterManager() {
  const [categories, setCategories] = useState<ParameterCategory[]>([]);
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [editingParam, setEditingParam] = useState<EditingParameter | null>(null);
  const updateParameter = useUpdateParameter();

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      const cats = await parameterService.getCategories();
      setCategories(cats);
      if (cats.length > 0 && !activeCategory) {
        setActiveCategory(cats[0].name);
      }
    };
    loadCategories();
  }, [activeCategory]);

  const { parameters, isLoading, error } = useParametersByCategory(activeCategory);

  const handleEdit = (param: Parameter) => {
    const isJsonType = param.type === 'object' || param.type === 'array';
    
    setEditingParam({
      key: param.key,
      value: isJsonType ? param.value : String(param.value),
      isJson: isJsonType
    });
  };

  const handleSave = async (param: Parameter) => {
    if (!editingParam || editingParam.key !== param.key) return;

    try {
      let parsedValue: any = editingParam.value;
      
      // Parse value based on type
      if (!editingParam.isJson) {
        if (param.type === 'number') {
          parsedValue = parseFloat(editingParam.value);
          if (isNaN(parsedValue)) {
            throw new Error('Invalid number format');
          }
        } else if (param.type === 'boolean') {
          parsedValue = editingParam.value === 'true';
        }
      }
      // For JSON types, value is already parsed by JsonParameterEditor

      // Validate against constraints
      if (param.type === 'number' && param.min_value !== null && parsedValue < param.min_value) {
        throw new Error(`Value must be at least ${param.min_value}`);
      }
      if (param.type === 'number' && param.max_value !== null && parsedValue > param.max_value) {
        throw new Error(`Value must be at most ${param.max_value}`);
      }

      await updateParameter.mutateAsync({
        key: param.key,
        value: parsedValue,
        changeReason: `Updated by admin`
      });

      setEditingParam(null);
      toast({
        title: "Success",
        description: `Parameter "${param.label}" updated successfully`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update parameter",
      });
    }
  };

  const handleCancel = () => {
    setEditingParam(null);
  };

  const renderParameterValue = (param: Parameter) => {
    const isEditing = editingParam?.key === param.key;
    const isJsonType = param.type === 'object' || param.type === 'array';

    if (isEditing) {
      if (editingParam.isJson) {
        return (
          <JsonParameterEditor
            value={editingParam.value}
            onChange={(newValue) => setEditingParam({ ...editingParam, value: newValue })}
            onSave={() => handleSave(param)}
            onCancel={handleCancel}
            paramKey={param.key}
          />
        );
      } else {
        return (
          <div className="flex items-center gap-2">
            <Input
              value={editingParam.value}
              onChange={(e) => setEditingParam({ ...editingParam, value: e.target.value })}
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleSave(param);
                if (e.key === 'Escape') handleCancel();
              }}
            />
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleSave(param)}
              disabled={updateParameter.isPending}
            >
              <Check className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCancel}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        );
      }
    }

    let displayValue: string;
    const value = parameterService.parseValue(param);
    
    if (isJsonType) {
      // Show a prettier summary for JSON objects
      if (param.key === 'leasing_rates') {
        const rates = value as Record<string, number>;
        displayValue = Object.entries(rates).map(([k, v]) => `${k}: €${v}`).join(', ');
      } else if (param.key === 'leasing_durations') {
        const durations = value as Array<any>;
        displayValue = durations.map(d => `${d.months} months${d.isPopular ? ' (popular)' : ''}`).join(', ');
      } else if (typeof value === 'object') {
        displayValue = JSON.stringify(value);
      } else {
        displayValue = String(value);
      }
    } else {
      displayValue = String(value);
    }

    return (
      <div className="flex items-center gap-2">
        <span className={`text-sm ${isJsonType ? 'bg-gray-100 p-2 rounded border' : 'font-mono'}`}>
          {displayValue}
          {param.unit && <span className="text-gray-500 ml-1">{param.unit}</span>}
        </span>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleEdit(param)}
          className="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <Edit2 className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-robot-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Error loading parameters: {error instanceof Error ? error.message : 'Unknown error'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeCategory} onValueChange={setActiveCategory}>
        <TabsList className="grid grid-cols-3 lg:grid-cols-6 gap-2">
          {categories.map((category) => {
            const Icon = categoryIcons[category.name] || Settings;
            return (
              <TabsTrigger
                key={category.id}
                value={category.name}
                className="flex items-center gap-2"
              >
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {category.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category.id} value={category.name} className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>{category.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</CardTitle>
                {category.description && (
                  <CardDescription>{category.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {parameters.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">
                      No parameters in this category
                    </p>
                  ) : (
                    parameters.map((param) => (
                      <div
                        key={param.id}
                        className="group p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="space-y-1 flex-1">
                            <div className="flex items-center gap-2">
                              <Label className="text-base font-medium">
                                {param.label}
                              </Label>
                              <Badge variant="outline" className="text-xs">
                                {param.type}
                              </Badge>
                              {param.key && (
                                <code className="text-xs bg-gray-100 px-1 py-0.5 rounded">
                                  {param.key}
                                </code>
                              )}
                            </div>
                            {param.description && (
                              <p className="text-sm text-gray-600">{param.description}</p>
                            )}
                            {(param.min_value !== null || param.max_value !== null) && (
                              <p className="text-xs text-gray-500">
                                Constraints: 
                                {param.min_value !== null && ` Min: ${param.min_value}`}
                                {param.max_value !== null && ` Max: ${param.max_value}`}
                                {param.step !== null && ` Step: ${param.step}`}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="mt-3">
                          {renderParameterValue(param)}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
} 