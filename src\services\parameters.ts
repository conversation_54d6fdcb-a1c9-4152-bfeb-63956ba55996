import { supabase, type Parameter, type ParameterCategory } from '@/lib/supabase';

export interface ParameterWithCategory extends Parameter {
  category?: ParameterCategory;
}

class ParameterService {
  private cache: Map<string, Parameter> = new Map();
  private lastFetch: number = 0;
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  async getAll(): Promise<Parameter[]> {
    try {
      const { data, error } = await supabase
        .from('parameters')
        .select('*')
        .eq('is_active', true)
        .order('key');

      if (error) throw error;
      
      // Update cache
      data?.forEach(param => {
        this.cache.set(param.key, param);
      });
      this.lastFetch = Date.now();
      
      return data || [];
    } catch (error) {
      console.error('Error fetching parameters:', error);
      // Return cached values if available
      return Array.from(this.cache.values());
    }
  }

  async getByCategory(categoryName: string): Promise<ParameterWithCategory[]> {
    try {
      // First get the category ID
      const { data: categoryData, error: categoryError } = await supabase
        .from('parameter_categories')
        .select('id')
        .eq('name', categoryName)
        .single();

      if (categoryError) throw categoryError;
      if (!categoryData) return [];

      // Then get parameters for that category
      const { data, error } = await supabase
        .from('parameters')
        .select(`
          *,
          category:parameter_categories!category_id(*)
        `)
        .eq('is_active', true)
        .eq('category_id', categoryData.id)
        .order('key');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching parameters by category:', error);
      return [];
    }
  }

  async getByKey(key: string): Promise<Parameter | null> {
    // Check cache first
    if (this.cache.has(key) && Date.now() - this.lastFetch < this.cacheTimeout) {
      return this.cache.get(key) || null;
    }

    try {
      const { data, error } = await supabase
        .from('parameters')
        .select('*')
        .eq('key', key)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      
      if (data) {
        this.cache.set(key, data);
      }
      
      return data;
    } catch (error) {
      console.error(`Error fetching parameter ${key}:`, error);
      return this.cache.get(key) || null;
    }
  }

  async getByKeys(keys: string[]): Promise<Record<string, Parameter>> {
    try {
      const { data, error } = await supabase
        .from('parameters')
        .select('*')
        .in('key', keys)
        .eq('is_active', true);

      if (error) throw error;
      
      const result: Record<string, Parameter> = {};
      data?.forEach(param => {
        result[param.key] = param;
        this.cache.set(param.key, param);
      });
      
      return result;
    } catch (error) {
      console.error('Error fetching parameters by keys:', error);
      
      // Return cached values
      const result: Record<string, Parameter> = {};
      keys.forEach(key => {
        const cached = this.cache.get(key);
        if (cached) result[key] = cached;
      });
      return result;
    }
  }

  async update(key: string, value: any, changeReason?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('parameters')
        .update({ 
          value: typeof value === 'object' ? JSON.stringify(value) : value,
          updated_at: new Date().toISOString()
        })
        .eq('key', key);

      if (error) throw error;
      
      // Update cache
      const param = this.cache.get(key);
      if (param) {
        param.value = value;
        param.updated_at = new Date().toISOString();
      }
    } catch (error) {
      console.error(`Error updating parameter ${key}:`, error);
      throw error;
    }
  }

  async bulkUpdate(updates: Array<{ key: string; value: any }>): Promise<void> {
    try {
      const promises = updates.map(({ key, value }) => 
        this.update(key, value)
      );
      
      await Promise.all(promises);
    } catch (error) {
      console.error('Error bulk updating parameters:', error);
      throw error;
    }
  }

  async getCategories(): Promise<ParameterCategory[]> {
    try {
      const { data, error } = await supabase
        .from('parameter_categories')
        .select('*')
        .order('display_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }

  // Parse parameter value based on type
  parseValue(param: Parameter): any {
    if (!param) return null;
    
    const { value, type, default_value } = param;
    
    try {
      switch (type) {
        case 'number':
          return typeof value === 'number' ? value : parseFloat(value);
        case 'boolean':
          return typeof value === 'boolean' ? value : value === 'true';
        case 'array':
        case 'object':
          return typeof value === 'string' ? JSON.parse(value) : value;
        default:
          return value;
      }
    } catch (error) {
      console.error(`Error parsing parameter ${param.key}:`, error);
      return default_value;
    }
  }

  // Subscribe to parameter changes
  subscribeToChanges(callback: (payload: any) => void) {
    return supabase
      .channel('parameter-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'parameters' },
        callback
      )
      .subscribe();
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    this.lastFetch = 0;
  }
}

export const parameterService = new ParameterService(); 