import { supabase } from '@/lib/supabase';
import type { Tables } from '@/lib/supabase';

export type CalculationDefault = Tables<'calculation_defaults'>;

interface LeasingRates {
  [key: string]: number;
}

interface LeasingDuration {
  id: string;
  months: number;
  isPopular?: boolean;
}

// Hardcoded UI constraints (no longer in database)
export const UI_CONSTRAINTS = {
  area: { min: 100, max: 10000, step: 100 },
  cleaningPerformance: {
    manual: { min: 50, max: 700, step: 10 },
    conventional: { min: 500, max: 1200, step: 100 }
  },
  hourlyRate: { min: 1, step: 0.5 },
  robotCoverage: { min: 70, max: 95 }
} as const;

// Comprehensive fallback defaults - ensures calculator works even if database is completely unavailable
export const FALLBACK_DEFAULTS = {
  // Leasing rates
  'leasing_rate_48_months': 492,
  'leasing_rate_60_months': 405,
  
  // Business rates
  'profit_margin_percentage': 23,
  'electricity_cost_per_kwh': 0.30,
  
  // Service and operational costs
  'service_cost': 1000,
  'training_cost': 1500,
  'maintenance_cost': 800,
  'insurance_cost': 500,
  'miscellaneous_cost': 200,
  
  // Default operational values
  'default_area': 1500,
  'default_performance': 500,
  'default_working_hours': 8,
  'default_working_days': 250,
  'default_labor_cost': 50,
  
  // Additional calculator defaults (legacy compatibility)
  'default_total_service_cost': 1000,
  'default_cleaning_performance': 500,
  'default_monthly_cleanings': 24,
  'hourly_rate_manual': 20,
  'default_number_of_robots': 1,
  'default_maintenance_cost': 169,
  'robot_coverage_default': 85,
  'conventional_machine_leasing_cost': 179,
  'conventional_machine_service_cost': 120,
  'hourly_rate_conventional': 23,
  'docking_station_fee': 69
} as const;

// Cache for parameters
let calculationDefaultsCache: CalculationDefault[] | null = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class BusinessParameterService {
  // Get all calculation defaults with caching and fallback support
  async getCalculationDefaults(): Promise<CalculationDefault[]> {
    const now = Date.now();
    if (calculationDefaultsCache && (now - cacheTimestamp) < CACHE_DURATION) {
      return calculationDefaultsCache;
    }

    try {
      const { data, error } = await supabase
        .from('calculation_defaults')
        .select('*')
        .order('category', { ascending: true })
        .order('parameter_key', { ascending: true });

      if (error) {
        console.error('Error fetching calculation defaults:', error);
        // Return empty array but don't cache it, so we can retry later
        return [];
      }

      calculationDefaultsCache = data || [];
      cacheTimestamp = now;
      return calculationDefaultsCache;
    } catch (err) {
      console.error('Database connection failed, calculator will work with fallback values:', err);
      // Return empty array so fallback values are used
      return [];
    }
  }

  // Get a specific calculation default by key with comprehensive fallback
  async getCalculationDefault(key: string, fallback: number): Promise<number> {
    try {
      const defaults = await this.getCalculationDefaults();
      const param = defaults.find(p => p.parameter_key === key);
      
      // Use database value if available, otherwise fallback to FALLBACK_DEFAULTS, then to provided fallback
      return param?.parameter_value ?? FALLBACK_DEFAULTS[key as keyof typeof FALLBACK_DEFAULTS] ?? fallback;
    } catch (error) {
      console.error(`Error getting calculation default ${key}:`, error);
      // Use FALLBACK_DEFAULTS if available, otherwise provided fallback
      return FALLBACK_DEFAULTS[key as keyof typeof FALLBACK_DEFAULTS] ?? fallback;
    }
  }

  // Get leasing rates with robust fallback support
  async getLeasingRates(): Promise<LeasingRates> {
    try {
      const defaults = await this.getCalculationDefaults();
      const rate48 = defaults.find(p => p.parameter_key === 'leasing_rate_48_months')?.parameter_value 
                   ?? FALLBACK_DEFAULTS.leasing_rate_48_months;
      const rate60 = defaults.find(p => p.parameter_key === 'leasing_rate_60_months')?.parameter_value 
                   ?? FALLBACK_DEFAULTS.leasing_rate_60_months;
      
      return {
        "lease-48": rate48,
        "lease-60": rate60
      };
    } catch (error) {
      console.error('Error getting leasing rates, using fallback values:', error);
      return {
        "lease-48": FALLBACK_DEFAULTS.leasing_rate_48_months,
        "lease-60": FALLBACK_DEFAULTS.leasing_rate_60_months
      };
    }
  }

  // Get leasing durations (simplified - hardcoded since always 2 options)
  async getLeasingDurations(): Promise<LeasingDuration[]> {
    // Always return the same 2 options since they won't change
    return [
      { id: "lease-48", months: 48, isPopular: true },
      { id: "lease-60", months: 60 }
    ];
  }

  // Update a calculation default
  async updateCalculationDefault(key: string, value: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('calculation_defaults')
        .update({ 
          parameter_value: value,
          updated_at: new Date().toISOString()
        })
        .eq('parameter_key', key);

      if (error) {
        console.error(`Error updating calculation default ${key}:`, error);
        return false;
      }

      // Invalidate cache
      calculationDefaultsCache = null;
      return true;
    } catch (error) {
      console.error(`Error updating calculation default ${key}:`, error);
      return false;
    }
  }

  // Clear cache (useful for real-time updates)
  clearCache() {
    calculationDefaultsCache = null;
    cacheTimestamp = 0;
  }

  // Subscribe to real-time updates
  subscribeToUpdates(callback: () => void) {
    const defaultsChannel = supabase
      .channel('calculation_defaults_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'calculation_defaults' },
        () => {
          this.clearCache();
          callback();
        })
      .subscribe();

    return () => {
      supabase.removeChannel(defaultsChannel);
    };
  }
}

export const businessParameterService = new BusinessParameterService(); 