import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Database types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      business_parameters: {
        Row: {
          category: string
          description: string | null
          display_name: string
          id: string
          parameter_key: string
          parameter_type: string
          parameter_value: Json
          updated_at: string | null
        }
        Insert: {
          category: string
          description?: string | null
          display_name: string
          id?: string
          parameter_key: string
          parameter_type: string
          parameter_value: Json
          updated_at?: string | null
        }
        Update: {
          category?: string
          description?: string | null
          display_name?: string
          id?: string
          parameter_key?: string
          parameter_type?: string
          parameter_value?: Json
          updated_at?: string | null
        }
      }
      calculation_defaults: {
        Row: {
          category: string
          description: string | null
          display_name: string
          id: string
          parameter_key: string
          parameter_value: number
          unit: string | null
          updated_at: string | null
        }
        Insert: {
          category: string
          description?: string | null
          display_name: string
          id?: string
          parameter_key: string
          parameter_value: number
          unit?: string | null
          updated_at?: string | null
        }
        Update: {
          category?: string
          description?: string | null
          display_name?: string
          id?: string
          parameter_key?: string
          parameter_value?: number
          unit?: string | null
          updated_at?: string | null
        }
      }
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Types for our database schema
export interface Parameter {
  id: string;
  key: string;
  value: any;
  type: 'number' | 'string' | 'boolean' | 'array' | 'object';
  category_id: string | null;
  label: string;
  description: string | null;
  unit: string | null;
  min_value: number | null;
  max_value: number | null;
  step: number | null;
  default_value: any;
  is_active: boolean;
  validation_rules: any;
  created_at: string;
  updated_at: string;
}

export interface ParameterCategory {
  id: string;
  name: string;
  description: string | null;
  display_order: number;
  icon: string | null;
  created_at: string;
  updated_at: string;
}

export interface ParameterHistory {
  id: string;
  parameter_id: string;
  old_value: any;
  new_value: any;
  changed_by: string | null;
  change_reason: string | null;
  changed_at: string;
} 