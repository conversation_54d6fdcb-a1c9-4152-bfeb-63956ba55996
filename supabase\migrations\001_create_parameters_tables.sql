-- Create parameter categories table
CREATE TABLE IF NOT EXISTS parameter_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    icon TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create parameters table
CREATE TABLE IF NOT EXISTS parameters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('number', 'string', 'boolean', 'array', 'object')),
    category_id UUID REFERENCES parameter_categories(id) ON DELETE SET NULL,
    label TEXT NOT NULL,
    description TEXT,
    unit TEXT,
    min_value NUMERIC,
    max_value NUMERIC,
    step NUMERIC,
    default_value JSONB,
    is_active BOOLEAN DEFAULT true,
    validation_rules JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create parameter history for audit trail
CREATE TABLE IF NOT EXISTS parameter_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parameter_id UUID REFERENCES parameters(id) ON DELETE CASCADE,
    old_value JSONB,
    new_value JSONB,
    changed_by UUID,
    change_reason TEXT,
    changed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_parameters_key ON parameters(key);
CREATE INDEX idx_parameters_category ON parameters(category_id);
CREATE INDEX idx_parameters_active ON parameters(is_active);
CREATE INDEX idx_parameter_history_parameter ON parameter_history(parameter_id);
CREATE INDEX idx_parameter_history_changed_at ON parameter_history(changed_at DESC);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_parameter_categories_updated_at BEFORE UPDATE ON parameter_categories
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_parameters_updated_at BEFORE UPDATE ON parameters
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Create function to log parameter changes
CREATE OR REPLACE FUNCTION log_parameter_change()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND OLD.value IS DISTINCT FROM NEW.value THEN
        INSERT INTO parameter_history (parameter_id, old_value, new_value, changed_by)
        VALUES (NEW.id, OLD.value, NEW.value, auth.uid());
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply parameter change trigger
CREATE TRIGGER log_parameter_changes AFTER UPDATE ON parameters
    FOR EACH ROW EXECUTE PROCEDURE log_parameter_change();

-- Row Level Security Policies
ALTER TABLE parameter_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE parameters ENABLE ROW LEVEL SECURITY;
ALTER TABLE parameter_history ENABLE ROW LEVEL SECURITY;

-- Public read access for active parameters
CREATE POLICY "Public can read active parameters" ON parameters
    FOR SELECT USING (is_active = true);

-- Admin full access (you'll need to set up admin role)
CREATE POLICY "Admins have full access to parameters" ON parameters
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins have full access to categories" ON parameter_categories
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins can read parameter history" ON parameter_history
    FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');

-- Insert initial categories
INSERT INTO parameter_categories (name, description, display_order, icon) VALUES
    ('leasing_options', 'Leasing rates and duration options', 1, 'calendar'),
    ('service_costs', 'Service and maintenance costs', 2, 'wrench'),
    ('performance_metrics', 'Cleaning performance and coverage metrics', 3, 'chart'),
    ('labor_costs', 'Hourly rates for different cleaning methods', 4, 'users'),
    ('default_values', 'Default form values and constraints', 5, 'settings'),
    ('ui_constraints', 'User interface constraints and limits', 6, 'sliders');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON parameters TO anon;
GRANT ALL ON parameters TO authenticated;
GRANT ALL ON parameter_categories TO authenticated;
GRANT ALL ON parameter_history TO authenticated; 