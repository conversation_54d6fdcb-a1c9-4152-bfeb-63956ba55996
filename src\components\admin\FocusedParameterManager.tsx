import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useBusinessParameters } from '@/hooks/useBusinessParameters';
import { businessParameterService } from '@/services/businessParameters';
import type { CalculationDefault } from '@/services/businessParameters';
import { 
  DollarSign, 
  Settings, 
  Activity, 
  Save,
  Edit2,
  X,
  Check,
  AlertCircle,
  CreditCard,
  Target,
  TrendingUp,
  Zap,
  RotateCcw
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface EditingCalculationDefault {
  parameter_key: string;
  parameter_value: number;
}

interface ValidationError {
  field: string;
  message: string;
  suggestedValue?: number;
}

interface ValidationConstraint {
  min?: number;
  max?: number;
  message?: string;
}

interface CategoryConfig {
  icon: React.ElementType;
  color: string;
  bgColor: string;
  borderColor: string;
  description: string;
  accentColor: string;
  label: string;
}

const categoryConfigs: Record<string, CategoryConfig> = {
  'rates': {
    icon: TrendingUp,
    color: 'text-green-700',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    accentColor: 'bg-green-500',
    description: 'Leasing-Raten und Geschäftspreise inklusive Roboter-Leasing',
    label: 'Raten'
  },
  'costs': {
    icon: CreditCard,
    color: 'text-blue-700',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    accentColor: 'bg-blue-500',
    description: 'Service- und Betriebskosten',
    label: 'Kosten'
  },
  'defaults': {
    icon: Target,
    color: 'text-purple-700',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    accentColor: 'bg-purple-500',
    description: 'Standard-Formularwerte und Anfangseinstellungen',
    label: 'Standardwerte'
  }
};

// Validierungsregeln basierend auf tatsächlichen UI_CONSTRAINTS und Geschäftslogik - ALLE Datenbankparameter abgedeckt
const VALIDATION_CONSTRAINTS: Record<string, ValidationConstraint> = {
  // RATES (Raten) - Leasing-Raten und Stundensätze
  'leasing_rate_48_months': { 
    min: 200, 
    max: 800, 
    message: 'Leasing-Rate sollte zwischen €200-€800/Monat liegen (aktueller Standard: €492)' 
  },
  'leasing_rate_60_months': { 
    min: 200, 
    max: 800, 
    message: 'Leasing-Rate sollte zwischen €200-€800/Monat liegen (aktueller Standard: €405)' 
  },
  'hourly_rate_manual': { 
    min: 1, 
    max: 100, 
    message: 'Stundensatz für manuelles Reinigen sollte zwischen €1-€100/Stunde liegen (entspricht Rechner-Einschränkungen)' 
  },
  'hourly_rate_conventional': { 
    min: 1, 
    max: 100, 
    message: 'Stundensatz für konventionelle Maschine sollte zwischen €1-€100/Stunde liegen (entspricht Rechner-Einschränkungen)' 
  },
  
  // COSTS (Kosten) - Service- und Betriebskosten
  'default_maintenance_cost': { 
    min: 50, 
    max: 1000, 
    message: 'Wartungskosten sollten zwischen €50-€1000/Monat liegen (aktueller Standard: €169)' 
  },
  'default_total_service_cost': { 
    min: 100, 
    max: 5000, 
    message: 'Servicekosten sollten zwischen €100-€5000/Monat liegen (aktueller Standard: €1000)' 
  },
  'conventional_machine_leasing_cost': { 
    min: 50, 
    max: 1000, 
    message: 'Konventionelle Leasingkosten sollten zwischen €50-€1000/Monat liegen (aktueller Standard: €179)' 
  },
  'conventional_machine_service_cost': { 
    min: 50, 
    max: 1000, 
    message: 'Konventionelle Servicekosten sollten zwischen €50-€1000/Monat liegen (aktueller Standard: €120)' 
  },
  'docking_station_fee': { 
    min: 0, 
    max: 200, 
    message: 'Dockingstation-Gebühr sollte zwischen €0-€200/Monat liegen (aktueller Standard: €69)' 
  },
  
  // DEFAULTS (Standardwerte) - Betriebsparameter basierend auf UI_CONSTRAINTS
  'default_area': { 
    min: 100, 
    max: 10000, 
    message: 'Fläche sollte zwischen 100-10000 m² liegen (entspricht Rechner-Einschränkungen)' 
  },
  'default_cleaning_performance': { 
    min: 50, 
    max: 1200, 
    message: 'Reinigungsleistung sollte zwischen 50-1200 m²/h liegen (entspricht Rechner-Einschränkungen)' 
  },
  'default_monthly_cleanings': { 
    min: 1, 
    max: 100, 
    message: 'Monatliche Reinigungen sollten zwischen 1-100 liegen (aktueller Standard: 24)' 
  },
  'default_number_of_robots': { 
    min: 1, 
    max: 20, 
    message: 'Anzahl Roboter sollte zwischen 1-20 liegen (aktueller Standard: 1)' 
  },
  'robot_coverage_default': { 
    min: 70, 
    max: 95, 
    message: 'Roboter-Abdeckungsgrad sollte zwischen 70%-95% liegen (entspricht Rechner-Einschränkungen, aktueller Standard: 85%)' 
  }
};

export default function FocusedParameterManager() {
  const { calculationDefaults, isLoading, error, refresh } = useBusinessParameters();
  const [editingDefault, setEditingDefault] = useState<EditingCalculationDefault | null>(null);
  const [updateLoading, setUpdateLoading] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('rates');
  const [resetLoading, setResetLoading] = useState(false);
  const [validationError, setValidationError] = useState<ValidationError | null>(null);

  const validateParameter = (key: string, value: number): ValidationError | null => {
    const constraint = VALIDATION_CONSTRAINTS[key];
    if (!constraint) return null;

    if (constraint.min !== undefined && value < constraint.min) {
      return {
        field: key,
        message: constraint.message || `Value must be at least ${constraint.min}`,
        suggestedValue: constraint.min
      };
    }

    if (constraint.max !== undefined && value > constraint.max) {
      return {
        field: key,
        message: constraint.message || `Value must not exceed ${constraint.max}`,
        suggestedValue: constraint.max
      };
    }

    return null;
  };

  const handleEditDefault = (param: CalculationDefault) => {
    setEditingDefault({
      parameter_key: param.parameter_key,
      parameter_value: param.parameter_value
    });
    setValidationError(null);
  };

  const handleSaveDefault = async (param: CalculationDefault) => {
    if (!editingDefault || editingDefault.parameter_key !== param.parameter_key) return;

    // Validate the value before saving
    const validation = validateParameter(editingDefault.parameter_key, editingDefault.parameter_value);
    if (validation) {
      setValidationError(validation);
      return;
    }

    try {
      setUpdateLoading(param.parameter_key);
      
      const success = await businessParameterService.updateCalculationDefault(
        param.parameter_key,
        editingDefault.parameter_value
      );

      if (success) {
        setEditingDefault(null);
        setValidationError(null);
        
        // Force refresh of data to show updated values immediately
        await refresh();
        
        toast({
          title: "Erfolgreich",
          description: `Parameter "${param.display_name}" erfolgreich aktualisiert`,
        });
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fehler",
        description: error instanceof Error ? error.message : "Parameter konnte nicht aktualisiert werden",
      });
    } finally {
      setUpdateLoading(null);
    }
  };

  const handleCancel = () => {
    setEditingDefault(null);
    setValidationError(null);
  };

  const handleAcceptSuggestion = () => {
    if (validationError?.suggestedValue !== undefined && editingDefault) {
      setEditingDefault({
        ...editingDefault,
        parameter_value: validationError.suggestedValue
      });
      setValidationError(null);
    }
  };

  const handleResetToDefaults = async () => {
    try {
      setResetLoading(true);
      
      // Default values for reset - only actual database parameters
      const defaultValues = {
        // Rates (Raten)
        'leasing_rate_48_months': 492,
        'leasing_rate_60_months': 405,
        'hourly_rate_manual': 20,
        'hourly_rate_conventional': 23,
        
        // Costs (Kosten)
        'default_maintenance_cost': 169,
        'default_total_service_cost': 1000,
        'conventional_machine_leasing_cost': 179,
        'conventional_machine_service_cost': 120,
        'docking_station_fee': 69,
        
        // Defaults (Standardwerte)
        'default_area': 1500,
        'default_cleaning_performance': 500,
        'default_monthly_cleanings': 24,
        'default_number_of_robots': 1,
        'robot_coverage_default': 85
      };

      // Reset all parameters to their default values
      const resetPromises = Object.entries(defaultValues).map(([key, value]) =>
        businessParameterService.updateCalculationDefault(key, value)
      );

      await Promise.all(resetPromises);

      // Force refresh of data to show updated values immediately
      await refresh();

      toast({
        title: "Erfolgreich",
        description: "Alle Parameter wurden auf ihre Standardwerte zurückgesetzt",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fehler",
        description: error instanceof Error ? error.message : "Parameter konnten nicht zurückgesetzt werden",
      });
    } finally {
      setResetLoading(false);
    }
  };

  const renderDefaultParameterValue = (param: CalculationDefault, categoryConfig: CategoryConfig) => {
    const isEditing = editingDefault?.parameter_key === param.parameter_key;
    const constraint = VALIDATION_CONSTRAINTS[param.parameter_key];

    if (isEditing) {
      return (
        <div className="space-y-2">
          <div className="flex items-center gap-2 p-3 bg-white rounded-lg border-2 border-orange-200">
            <div className="flex-1">
              <Input
                type="number"
                value={editingDefault.parameter_value}
                onChange={(e) => {
                  const value = parseFloat(e.target.value) || 0;
                  setEditingDefault({ 
                    ...editingDefault, 
                    parameter_value: value 
                  });
                  // Clear validation error when user types
                  if (validationError?.field === param.parameter_key) {
                    setValidationError(null);
                  }
                }}
                className="border-orange-300 focus:border-orange-500"
                step={param.parameter_key.includes('rate') || param.parameter_key.includes('electricity') ? 0.01 : 1}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSaveDefault(param);
                  if (e.key === 'Escape') handleCancel();
                }}
              />
            </div>
            <Button
              size="sm"
              className="bg-green-600 hover:bg-green-700 text-white"
              onClick={() => handleSaveDefault(param)}
              disabled={updateLoading === param.parameter_key}
            >
              <Check className="h-4 w-4" />
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              className="border-gray-300 hover:bg-gray-50"
              onClick={handleCancel}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Beschränkungshinweis */}
          {constraint && (
            <p className="text-xs text-gray-500 px-3">
              Gültiger Bereich: {constraint.min !== undefined ? `${constraint.min}` : 'kein Min'} - {constraint.max !== undefined ? `${constraint.max}` : 'kein Max'}
              {param.unit && ` ${param.unit}`}
            </p>
          )}
        </div>
      );
    }

    return (
      <div className="group flex items-center justify-between p-3 bg-white/60 rounded-lg border hover:bg-white hover:shadow-sm transition-all">
        <div className="flex items-center gap-3">
          <div className={`w-2 h-8 ${categoryConfig.accentColor} rounded-full`} />
          <div>
            <div className="flex items-center gap-2">
              <span className="font-mono text-lg font-semibold">
                {param.parameter_value}
              </span>
              {param.unit && (
                <Badge variant="outline" className="text-xs">
                  {param.unit}
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1">{param.parameter_key}</p>
          </div>
        </div>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleEditDefault(param)}
          className="opacity-0 group-hover:opacity-100 transition-opacity hover:bg-gray-100"
        >
          <Edit2 className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Parameter werden geladen...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
          Fehler beim Laden der Parameter: {error}
        </AlertDescription>
        </Alert>
        
        {error.includes('default values') && (
          <Alert className="border-orange-200 bg-orange-50">
            <AlertCircle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <strong>Rechner funktioniert weiterhin:</strong> Der ROI-Rechner arbeitet weiterhin mit eingebauten Standardwerten. 
              Das Admin-Panel ist nicht verfügbar, bis die Datenbankverbindung wiederhergestellt ist.
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  }

  const groupedDefaults = calculationDefaults.reduce((acc, param) => {
    if (!acc[param.category]) acc[param.category] = [];
    acc[param.category].push(param);
    return acc;
  }, {} as Record<string, CalculationDefault[]>);

  // Sort parameters within each category to maintain consistent order
  Object.keys(groupedDefaults).forEach(category => {
    groupedDefaults[category].sort((a, b) => a.parameter_key.localeCompare(b.parameter_key));
  });

  // Sort categories in specific order
  const sortedCategories = ['rates', 'costs', 'defaults'];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Geschäftsparameter-Verwaltung</h1>
                <p className="text-gray-600 mt-1">
                  Konfigurieren Sie geschäftskritische Berechnungsparameter mit vereinfachten Steuerelementen
                </p>
              </div>
            </div>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2 border-red-200 text-red-700 hover:bg-red-50">
                  <RotateCcw className="h-4 w-4" />
                  Auf Standardwerte zurücksetzen
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Alle Parameter auf Standardwerte zurücksetzen?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Diese Aktion setzt alle Geschäftsparameter auf ihre ursprünglichen Standardwerte zurück. 
                    Dies kann nicht rückgängig gemacht werden. Sind Sie sicher, dass Sie fortfahren möchten?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={handleResetToDefaults}
                    disabled={resetLoading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {resetLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Wird zurückgesetzt...
                      </>
                    ) : (
                      <>
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Alle zurücksetzen
                      </>
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        
        {/* Fallback-Warnung anzeigen, wenn keine Daten geladen wurden */}
        {calculationDefaults.length === 0 && !isLoading && (
          <Alert className="mb-6 border-orange-200 bg-orange-50">
            <AlertCircle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <strong>Läuft mit Standardwerten:</strong> Datenbankverbindung nicht verfügbar. 
              Der Rechner arbeitet mit Standardwerten, aber Admin-Änderungen können nicht gespeichert werden, bis die Verbindung wiederhergestellt ist.
            </AlertDescription>
          </Alert>
        )}

        {/* Validierungsfehler-Dialog */}
        <Dialog open={!!validationError} onOpenChange={() => setValidationError(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-red-600">
                <AlertCircle className="h-5 w-5" />
                Ungültiger Parameterwert
              </DialogTitle>
              <DialogDescription className="space-y-3">
                <p>{validationError?.message}</p>
                {validationError?.suggestedValue !== undefined && (
                  <p className="text-sm">
                    <strong>Vorgeschlagener Wert:</strong> {validationError.suggestedValue}
                    {calculationDefaults.find(p => p.parameter_key === validationError.field)?.unit && 
                      ` ${calculationDefaults.find(p => p.parameter_key === validationError.field)?.unit}`
                    }
                  </p>
                )}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-2">
              <Button variant="outline" onClick={() => setValidationError(null)}>
                Weiter bearbeiten
              </Button>
              {validationError?.suggestedValue !== undefined && (
                <Button onClick={handleAcceptSuggestion} className="bg-blue-600 hover:bg-blue-700">
                  Vorgeschlagenen Wert verwenden
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Tabbed Parameter Categories */}
        <Card className="border-2 border-gray-200 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-gray-100 p-1 h-auto">
              {sortedCategories.map((category) => {
                const config = categoryConfigs[category];
                const Icon = config.icon;
                
                return (
                  <TabsTrigger 
                    key={category}
                    value={category}
                    className="flex items-center gap-2 p-4 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                  >
                    <Icon className={`h-5 w-5 ${config.color}`} />
                    <span className="font-medium">{config.label}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {sortedCategories.map((category) => {
              const params = groupedDefaults[category] || [];
              const config = categoryConfigs[category];
              
              if (params.length === 0) return null;

              return (
                <TabsContent key={category} value={category} className="mt-0">
                  <div className={`${config.bgColor} border-b ${config.borderColor} p-6`}>
                    <div className="flex items-center gap-4">
                      <div className={`p-3 bg-white rounded-xl shadow-sm border ${config.borderColor}`}>
                        <config.icon className={`h-6 w-6 ${config.color}`} />
                      </div>
                      <div className="flex-1">
                        <h2 className={`text-xl font-bold ${config.color} capitalize`}>
                          {config.label} Konfiguration
                        </h2>
                        <p className="text-gray-600 mt-1">
                          {config.description}
                        </p>
                      </div>
                      <Badge variant="outline" className={`${config.color} border-current`}>
                        {params.length} Parameter
                      </Badge>
                    </div>
                  </div>
                  
                  <div className={`p-6 ${config.bgColor}/30`}>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {params.map((param) => (
                        <div key={param.id} className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <Label className="text-base font-semibold text-gray-900">
                                {param.display_name}
                              </Label>
                              {param.description && (
                                <p className="text-sm text-gray-600 mt-1">{param.description}</p>
                              )}
                            </div>
                          </div>
                          <div className="mt-3">
                            {renderDefaultParameterValue(param, config)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
              );
            })}
          </Tabs>
        </Card>
      </div>
    </div>
  );
} 