import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';

interface AdminAuthState {
  user: User | null;
  isAdmin: boolean;
  isLoading: boolean;
  error: string | null;
}

export function useAdminAuth() {
  const [state, setState] = useState<AdminAuthState>({
    user: null,
    isAdmin: false,
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    // Check current auth session
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) throw error;
        
        if (session?.user) {
          // Any authenticated user is admin
          setState({
            user: session.user,
            isAdmin: true,
            isLoading: false,
            error: null,
          });
        } else {
          setState({
            user: null,
            isAdmin: false,
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error('Error checking session:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to check session',
        }));
      }
    };

    checkSession();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          // Any authenticated user is admin
          setState({
            user: session.user,
            isAdmin: true,
            isLoading: false,
            error: null,
          });
        } else {
          setState({
            user: null,
            isAdmin: false,
            isLoading: false,
            error: null,
          });
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Any authenticated user is admin - no additional checks needed

      return { success: true };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to sign in';
      setState(prev => ({ ...prev, isLoading: false, error: message }));
      return { success: false, error: message };
    }
  };

  const signOut = async () => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();
      
      // Clear authentication state immediately
      setState({
        user: null,
        isAdmin: false,
        isLoading: false,
        error: null,
      });

      // Clear browser cache and history
      if (typeof window !== 'undefined') {
        // Clear session storage
        sessionStorage.clear();
        
        // Clear local storage
        localStorage.clear();
        
        // Force page reload to clear any cached state
        window.location.replace('/admin-login');
      }
    } catch (error) {
      console.error('Error signing out:', error);
      // Even if logout fails, clear local state and redirect
      setState({
        user: null,
        isAdmin: false,
        isLoading: false,
        error: null,
      });
      if (typeof window !== 'undefined') {
        window.location.replace('/admin-login');
      }
    }
  };

  return {
    ...state,
    signIn,
    signOut,
  };
} 