# Admin Panel Setup and Usage Guide

## Overview
The Admin Panel allows administrators to dynamically manage calculation parameters for the ROI Calculator without requiring code changes. All parameters that were previously hardcoded are now stored in a Supabase database and can be updated in real-time.

## Setup Instructions

### 1. Supabase Project Setup

1. Create a new Supabase project at [https://supabase.com](https://supabase.com)
2. Note your project URL and anon key from the project settings

### 2. Environment Configuration

Create a `.env` file in the project root:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Database Setup

Apply the migrations to your Supabase project:

1. Go to the SQL Editor in your Supabase dashboard
2. Run the migrations in order:
   - First run `supabase/migrations/001_create_parameters_tables.sql`
   - Then run `supabase/migrations/002_insert_initial_parameters.sql`

### 4. Admin User Setup

To create an admin user:

1. Go to Authentication → Users in Supabase
2. Create a new user with an email ending in `@admin.com` (e.g., `<EMAIL>`)
3. Or update an existing user's metadata to include `role: 'admin'`

Example SQL to update user metadata:
```sql
UPDATE auth.users 
SET raw_user_meta_data = jsonb_set(raw_user_meta_data, '{role}', '"admin"')
WHERE email = '<EMAIL>';
```

## Accessing the Admin Panel

1. Navigate to `/admin` in your browser
2. If not authenticated, you'll be redirected to `/admin/login`
3. Sign in with your admin credentials

## Admin Panel Features

### Parameter Management

The admin panel organizes parameters into categories:

- **Leasing Options**: Monthly rates for different contract durations
- **Service Costs**: Default service, maintenance, and docking station fees
- **Performance Metrics**: Robot coverage limits and cleaning performance ranges
- **Labor Costs**: Hourly rates for different cleaning methods
- **Default Values**: Initial form values for the calculator
- **UI Constraints**: Min/max values and steps for input fields

### Editing Parameters

1. Click on any parameter category tab
2. Click the edit icon next to a parameter value
3. **For simple values**: Enter the new value directly
4. **For JSON objects/arrays**: Use the smart JSON editor with visual forms
5. Press Enter or click the check mark to save
6. Press Escape or click the X to cancel

### Smart JSON Parameter Editor

For complex parameters (objects and arrays), the admin panel provides a user-friendly editor instead of raw JSON:

**Object Parameters** (like `leasing_rates`):
- Edit individual key-value pairs with proper input types
- Add/remove properties dynamically
- Automatic type detection (number, text, boolean)
- Visual preview of the resulting JSON

**Array Parameters** (like `leasing_durations`):
- Manage array items with structured forms
- Add/remove items easily
- Automatic structure detection for complex objects
- Support for nested properties

**Benefits**:
- No need to manually edit JSON syntax
- Reduced risk of syntax errors
- Type-safe input validation
- Immediate visual feedback

### Parameter Types

- **Number**: Single numeric values (e.g., hourly rates, costs)
- **Object**: Key-value pairs (e.g., leasing rates by duration)
- **Array**: Lists of values (e.g., leasing duration options)

## Real-time Updates

Changes made in the admin panel are immediately reflected in the calculator:
- No page refresh required
- All active users see updates instantly
- Changes are cached locally for performance

## Parameter Reference

### Key Parameters

| Parameter Key | Description | Type | Example |
|--------------|-------------|------|---------|
| `leasing_rates` | Monthly rates by contract duration | object | `{"lease-48": 492, "lease-60": 405}` |
| `docking_station_fee` | Monthly fee per robot | number | `69` |
| `robot_coverage_min` | Minimum robot coverage % | number | `70` |
| `robot_coverage_max` | Maximum robot coverage % | number | `95` |
| `hourly_rate_manual` | Manual cleaning hourly rate | number | `20` |
| `default_area` | Default cleaning area | number | `1500` |

### Constraints

Some parameters have validation rules:
- Numeric parameters may have min/max values
- Percentage values are typically 0-100
- Step values control input increments

## Security

- Only authenticated admin users can access the panel
- Row Level Security (RLS) policies protect the database
- Public users have read-only access to active parameters
- All changes are logged in the parameter history table

## Troubleshooting

### Common Issues

1. **"Missing Supabase environment variables" error**
   - Ensure `.env` file exists with correct values
   - Restart the development server after adding env variables

2. **Cannot sign in as admin**
   - Verify user email ends with `@admin.com` or has admin role
   - Check Supabase authentication settings

3. **Parameters not updating**
   - Check browser console for errors
   - Verify Supabase connection
   - Ensure RLS policies are correctly set

### Database Verification

Run this query to check if parameters are loaded:
```sql
SELECT * FROM parameters ORDER BY category_id, key;
```

## Development

### Adding New Parameters

1. Insert into the `parameters` table with appropriate category
2. Update `useParameters` hook fallback values
3. Use the parameter in your component:
   ```typescript
   const { getParameter } = useParameters();
   const myValue = getParameter('parameter_key', defaultValue);
   ```

### Creating New Categories

1. Insert into `parameter_categories` table
2. Add icon mapping in `ParameterManager.tsx`
3. Parameters will automatically appear under the new category

## Best Practices

1. **Always provide fallback values** when using parameters
2. **Test changes** in a development environment first
3. **Document** any new parameters you add
4. **Use appropriate types** (number vs string vs object)
5. **Set reasonable constraints** to prevent invalid inputs

## Future Enhancements

- [ ] Bulk import/export functionality
- [ ] Parameter change history viewer
- [ ] Role-based access control
- [ ] Parameter versioning
- [ ] A/B testing support 