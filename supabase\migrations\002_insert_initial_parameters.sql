-- Insert initial parameters based on hardcoded values from the codebase

-- Get category IDs
DO $$
DECLARE
    leasing_cat_id UUID;
    service_cat_id UUID;
    performance_cat_id UUID;
    labor_cat_id UUID;
    default_cat_id UUID;
    ui_cat_id UUID;
BEGIN
    SELECT id INTO leasing_cat_id FROM parameter_categories WHERE name = 'leasing_options';
    SELECT id INTO service_cat_id FROM parameter_categories WHERE name = 'service_costs';
    SELECT id INTO performance_cat_id FROM parameter_categories WHERE name = 'performance_metrics';
    SELECT id INTO labor_cat_id FROM parameter_categories WHERE name = 'labor_costs';
    SELECT id INTO default_cat_id FROM parameter_categories WHERE name = 'default_values';
    SELECT id INTO ui_cat_id FROM parameter_categories WHERE name = 'ui_constraints';

    -- Leasing Options
    INSERT INTO parameters (key, value, type, category_id, label, description, unit) VALUES
    ('leasing_rates', '{"lease-48": 492, "lease-60": 405}', 'object', leasing_cat_id, 
     'Leasing Rates', 'Monthly leasing rates for different contract durations', '€/month'),
    
    ('leasing_durations', '[{"id": "lease-48", "months": 48, "isPopular": true}, {"id": "lease-60", "months": 60}]', 
     'array', leasing_cat_id, 'Leasing Durations', 'Available leasing contract durations', null);

    -- Service Costs
    INSERT INTO parameters (key, value, type, category_id, label, description, unit, min_value, default_value) VALUES
    ('docking_station_fee', '69', 'number', service_cat_id, 
     'Docking Station Fee', 'Monthly fee per robot for docking station', '€/month', 0, '69'),
    
    ('default_maintenance_cost', '169', 'number', service_cat_id, 
     'Default Maintenance Cost', 'Default monthly maintenance cost per robot', '€/month', 0, '169'),
    
    ('default_total_service_cost', '1000', 'number', service_cat_id, 
     'Default Total Service Cost', 'Default monthly cost for external cleaning service', '€/month', 0, '1000');

    -- Performance Metrics
    INSERT INTO parameters (key, value, type, category_id, label, description, unit, min_value, max_value, default_value) VALUES
    ('robot_coverage_min', '70', 'number', performance_cat_id, 
     'Minimum Robot Coverage', 'Minimum percentage of area that robots can clean', '%', 0, 100, '70'),
    
    ('robot_coverage_max', '95', 'number', performance_cat_id, 
     'Maximum Robot Coverage', 'Maximum percentage of area that robots can clean', '%', 0, 100, '95'),
    
    ('robot_coverage_default', '85', 'number', performance_cat_id, 
     'Default Robot Coverage', 'Default percentage of area that robots clean', '%', 70, 95, '85');

    -- Labor Costs
    INSERT INTO parameters (key, value, type, category_id, label, description, unit, min_value, default_value) VALUES
    ('hourly_rate_manual', '20', 'number', labor_cat_id, 
     'Manual Cleaning Hourly Rate', 'Hourly rate for manual cleaning personnel', '€/h', 1, '20'),
    
    ('hourly_rate_conventional', '23', 'number', labor_cat_id, 
     'Conventional Machine Operator Rate', 'Hourly rate for conventional machine operators', '€/h', 1, '23');

    -- Default Values
    INSERT INTO parameters (key, value, type, category_id, label, description, unit, min_value, max_value, step, default_value) VALUES
    ('default_area', '1500', 'number', default_cat_id, 
     'Default Cleaning Area', 'Default area to be cleaned', 'm²', 100, 10000, 100, '1500'),
    
    ('default_cleaning_performance', '500', 'number', default_cat_id, 
     'Default Cleaning Performance', 'Default cleaning performance rate', 'm²/h', 50, 1200, 10, '500'),
    
    ('default_monthly_cleanings', '24', 'number', default_cat_id, 
     'Default Monthly Cleanings', 'Default number of cleanings per month', null, 1, null, 1, '24'),
    
    ('default_number_of_robots', '1', 'number', default_cat_id, 
     'Default Number of Robots', 'Default number of robots to deploy', null, 1, null, 1, '1'),
    
    ('conventional_machine_leasing_cost', '179', 'number', default_cat_id, 
     'Conventional Machine Leasing Cost', 'Monthly leasing cost for conventional cleaning machine', '€/month', 0, null, 10, '179'),
    
    ('conventional_machine_service_cost', '120', 'number', default_cat_id, 
     'Conventional Machine Service Cost', 'Monthly service cost for conventional cleaning machine', '€/month', 0, null, 10, '120');

    -- UI Constraints
    INSERT INTO parameters (key, value, type, category_id, label, description, validation_rules) VALUES
    ('area_constraints', '{"min": 100, "max": 10000, "step": 100}', 'object', ui_cat_id, 
     'Area Input Constraints', 'Constraints for area input field', null),
    
    ('cleaning_performance_constraints', '{"manual": {"min": 50, "max": 700, "step": 10}, "conventional": {"min": 500, "max": 1200, "step": 100}}', 
     'object', ui_cat_id, 'Cleaning Performance Constraints', 'Constraints for cleaning performance by method', null),
    
    ('hourly_rate_constraints', '{"min": 1, "step": 0.5}', 'object', ui_cat_id, 
     'Hourly Rate Constraints', 'Constraints for hourly rate inputs', null);

END $$; 