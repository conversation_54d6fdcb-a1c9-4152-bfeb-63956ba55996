import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Minus, Save, X } from 'lucide-react';

interface JsonParameterEditorProps {
  value: any;
  onChange: (value: any) => void;
  onSave: () => void;
  onCancel: () => void;
  paramKey: string;
}

interface KeyValuePair {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean';
}

export default function JsonParameterEditor({ 
  value, 
  onChange, 
  onSave, 
  onCancel, 
  paramKey 
}: JsonParameterEditorProps) {
  const [editMode, setEditMode] = useState<'object' | 'array' | 'raw'>('raw');
  const [objectData, setObjectData] = useState<KeyValuePair[]>([]);
  const [arrayData, setArrayData] = useState<any[]>([]);

  useEffect(() => {
    if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        setEditMode('array');
        setArrayData([...value]);
      } else {
        setEditMode('object');
        // Convert object to key-value pairs for editing
        const pairs: KeyValuePair[] = Object.entries(value).map(([key, val]) => ({
          key,
          value: val,
          type: typeof val as 'string' | 'number' | 'boolean'
        }));
        setObjectData(pairs);
      }
    } else {
      setEditMode('raw');
    }
  }, [value]);

  const handleObjectChange = (index: number, field: 'key' | 'value', newValue: any) => {
    const updated = [...objectData];
    if (field === 'key') {
      updated[index].key = newValue;
    } else {
      // Convert value based on type
      if (updated[index].type === 'number') {
        updated[index].value = parseFloat(newValue) || 0;
      } else if (updated[index].type === 'boolean') {
        updated[index].value = newValue;
      } else {
        updated[index].value = newValue;
      }
    }
    setObjectData(updated);
    
    // Convert back to object
    const obj: any = {};
    updated.forEach(pair => {
      obj[pair.key] = pair.value;
    });
    onChange(obj);
  };

  const addObjectProperty = () => {
    const newPair: KeyValuePair = { key: '', value: '', type: 'string' };
    setObjectData([...objectData, newPair]);
  };

  const removeObjectProperty = (index: number) => {
    const updated = objectData.filter((_, i) => i !== index);
    setObjectData(updated);
    
    const obj: any = {};
    updated.forEach(pair => {
      obj[pair.key] = pair.value;
    });
    onChange(obj);
  };

  const handleArrayItemChange = (index: number, newValue: any) => {
    const updated = [...arrayData];
    updated[index] = newValue;
    setArrayData(updated);
    onChange(updated);
  };

  const addArrayItem = () => {
    // Try to detect the structure from existing items
    if (arrayData.length > 0) {
      const firstItem = arrayData[0];
      if (typeof firstItem === 'object') {
        // Clone the structure of the first item
        const newItem = JSON.parse(JSON.stringify(firstItem));
        setArrayData([...arrayData, newItem]);
      } else {
        setArrayData([...arrayData, '']);
      }
    } else {
      setArrayData(['']);
    }
  };

  const removeArrayItem = (index: number) => {
    const updated = arrayData.filter((_, i) => i !== index);
    setArrayData(updated);
    onChange(updated);
  };

  const renderObjectEditor = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Object Properties</Label>
        <Button size="sm" variant="outline" onClick={addObjectProperty}>
          <Plus className="h-4 w-4 mr-1" />
          Add Property
        </Button>
      </div>
      
      {objectData.map((pair, index) => (
        <div key={index} className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
          <div className="flex-1">
            <Label className="text-xs text-gray-500">Key</Label>
            <Input
              value={pair.key}
              onChange={(e) => handleObjectChange(index, 'key', e.target.value)}
              placeholder="Property key"
              className="mt-1"
            />
          </div>
          
          <div className="flex-1">
            <Label className="text-xs text-gray-500">Value ({pair.type})</Label>
            {pair.type === 'boolean' ? (
              <div className="mt-1 flex items-center space-x-2">
                <Switch
                  checked={pair.value}
                  onCheckedChange={(checked) => handleObjectChange(index, 'value', checked)}
                />
                <span className="text-sm">{pair.value ? 'true' : 'false'}</span>
              </div>
            ) : (
              <Input
                type={pair.type === 'number' ? 'number' : 'text'}
                value={pair.value}
                onChange={(e) => handleObjectChange(index, 'value', e.target.value)}
                placeholder="Property value"
                className="mt-1"
              />
            )}
          </div>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => removeObjectProperty(index)}
            className="text-red-500 hover:text-red-700"
          >
            <Minus className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );

  const renderArrayEditor = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Array Items</Label>
        <Button size="sm" variant="outline" onClick={addArrayItem}>
          <Plus className="h-4 w-4 mr-1" />
          Add Item
        </Button>
      </div>
      
      {arrayData.map((item, index) => (
        <Card key={index} className="p-3">
          <div className="flex items-start gap-2">
            <div className="flex-1">
              <Label className="text-xs text-gray-500">Item {index + 1}</Label>
              {typeof item === 'object' ? (
                <div className="mt-2 space-y-2">
                  {Object.entries(item).map(([key, val]) => (
                    <div key={key} className="flex items-center gap-2">
                      <Label className="text-xs min-w-16">{key}:</Label>
                      {typeof val === 'boolean' ? (
                        <Switch
                          checked={val as boolean}
                          onCheckedChange={(checked) => {
                            const updated = { ...item, [key]: checked };
                            handleArrayItemChange(index, updated);
                          }}
                        />
                      ) : (
                                                 <Input
                           type={typeof val === 'number' ? 'number' : 'text'}
                          value={val as string | number}
                          onChange={(e) => {
                            const newVal = typeof val === 'number' 
                              ? parseFloat(e.target.value) || 0 
                              : e.target.value;
                            const updated = { ...item, [key]: newVal };
                            handleArrayItemChange(index, updated);
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <Input
                  value={item}
                  onChange={(e) => handleArrayItemChange(index, e.target.value)}
                  placeholder="Array item value"
                  className="mt-1"
                />
              )}
            </div>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={() => removeArrayItem(index)}
              className="text-red-500 hover:text-red-700"
            >
              <Minus className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      ))}
    </div>
  );

  const renderRawEditor = () => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">Raw JSON</Label>
      <textarea
        value={JSON.stringify(value, null, 2)}
        onChange={(e) => {
          try {
            const parsed = JSON.parse(e.target.value);
            onChange(parsed);
          } catch (error) {
            // Invalid JSON, don't update
          }
        }}
        className="w-full p-3 border rounded-md font-mono text-sm"
        rows={6}
        placeholder="Enter valid JSON"
      />
    </div>
  );

  const getSpecialParameterEditor = () => {
    // Special handling for known parameter structures
    switch (paramKey) {
      case 'leasing_rates':
        return renderObjectEditor();
      case 'leasing_durations':
        return renderArrayEditor();
      case 'area_constraints':
      case 'hourly_rate_constraints':
        return renderObjectEditor();
      case 'cleaning_performance_constraints':
        return renderRawEditor(); // Complex nested structure
      default:
        return editMode === 'object' ? renderObjectEditor() :
               editMode === 'array' ? renderArrayEditor() :
               renderRawEditor();
    }
  };

  return (
    <div className="space-y-4 p-4 bg-white border rounded-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant="outline">{editMode}</Badge>
          <span className="text-sm text-gray-500">
            Editing {paramKey}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline" onClick={onCancel}>
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
          <Button size="sm" onClick={onSave}>
            <Save className="h-4 w-4 mr-1" />
            Save
          </Button>
        </div>
      </div>

      {getSpecialParameterEditor()}
      
      <div className="pt-2 border-t">
        <Label className="text-xs text-gray-500">Preview JSON:</Label>
        <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
          {JSON.stringify(value, null, 2)}
        </pre>
      </div>
    </div>
  );
} 