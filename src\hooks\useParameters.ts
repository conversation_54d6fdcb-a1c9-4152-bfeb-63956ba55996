import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { parameterService, type ParameterWithCategory } from '@/services/parameters';
import { type Parameter } from '@/lib/supabase';

// Fallback values in case database is unavailable
const FALLBACK_PARAMETERS: Record<string, any> = {
  // Leasing rates
  'leasing_rates': { 'lease-48': 492, 'lease-60': 405 },
  'leasing_durations': [
    { id: 'lease-48', months: 48, isPopular: true },
    { id: 'lease-60', months: 60 }
  ],
  
  // Service costs
  'docking_station_fee': 69,
  'default_maintenance_cost': 169,
  'default_total_service_cost': 1000,
  
  // Performance metrics
  'robot_coverage_min': 70,
  'robot_coverage_max': 95,
  'robot_coverage_default': 85,
  
  // Labor costs
  'hourly_rate_manual': 20,
  'hourly_rate_conventional': 23,
  
  // Default values
  'default_area': 1500,
  'default_cleaning_performance': 500,
  'default_monthly_cleanings': 24,
  'default_number_of_robots': 1,
  'conventional_machine_leasing_cost': 179,
  'conventional_machine_service_cost': 120,
  
  // UI constraints
  'area_constraints': { min: 100, max: 10000, step: 100 },
  'cleaning_performance_constraints': {
    manual: { min: 50, max: 700, step: 10 },
    conventional: { min: 500, max: 1200, step: 100 }
  },
  'hourly_rate_constraints': { min: 1, step: 0.5 }
};

export function useParameters() {
  const queryClient = useQueryClient();
  
  // Fetch all parameters
  const { data: parameters = [], isLoading, error } = useQuery({
    queryKey: ['parameters'],
    queryFn: () => parameterService.getAll(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
  
  // Subscribe to real-time changes
  useEffect(() => {
    const subscription = parameterService.subscribeToChanges((payload) => {
      console.log('Parameter changed:', payload);
      queryClient.invalidateQueries({ queryKey: ['parameters'] });
    });
    
    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient]);
  
  // Convert parameters array to key-value object
  const parameterMap = useMemo(() => {
    const map: Record<string, any> = {};
    if (Array.isArray(parameters)) {
      parameters.forEach(param => {
        map[param.key] = parameterService.parseValue(param);
      });
    }
    return map;
  }, [parameters]);
  
  // Get parameter value with fallback
  const getParameter = (key: string, defaultValue?: any) => {
    if (parameterMap[key] !== undefined) {
      return parameterMap[key];
    }
    if (FALLBACK_PARAMETERS[key] !== undefined) {
      return FALLBACK_PARAMETERS[key];
    }
    return defaultValue;
  };
  
  return {
    parameters: parameterMap,
    isLoading,
    error,
    getParameter,
    fallbackParameters: FALLBACK_PARAMETERS
  };
}

export function useParametersByCategory(categoryName: string) {
  const { data: parameters = [], isLoading, error } = useQuery({
    queryKey: ['parameters', 'category', categoryName],
    queryFn: () => parameterService.getByCategory(categoryName),
    staleTime: 5 * 60 * 1000,
  });
  
  return { parameters, isLoading, error };
}

export function useUpdateParameter() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ key, value, changeReason }: { key: string; value: any; changeReason?: string }) =>
      parameterService.update(key, value, changeReason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parameters'] });
    },
  });
}

export function useBulkUpdateParameters() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (updates: Array<{ key: string; value: any }>) =>
      parameterService.bulkUpdate(updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parameters'] });
    },
  });
} 