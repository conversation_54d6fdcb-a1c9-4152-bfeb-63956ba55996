import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FALLBACK_DEFAULTS } from '@/services/businessParameters';
import { CheckCircle, AlertCircle } from 'lucide-react';

export default function OfflineTest() {
  // Critical parameters needed for calculator to function
  const criticalParams = [
    'leasing_rate_48_months',
    'leasing_rate_60_months', 
    'default_area',
    'default_performance',
    'default_monthly_cleanings',
    'hourly_rate_manual',
    'default_maintenance_cost',
    'robot_coverage_default',
    'docking_station_fee'
  ];

  const missingParams = criticalParams.filter(param => 
    !(param in FALLBACK_DEFAULTS) || FALLBACK_DEFAULTS[param as keyof typeof FALLBACK_DEFAULTS] === undefined
  );

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {missingParams.length === 0 ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <AlertCircle className="h-5 w-5 text-red-600" />
          )}
          Offline Calculator Readiness
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Fallback Coverage Status</h3>
          <div className="flex items-center gap-2">
            <Badge variant={missingParams.length === 0 ? "default" : "destructive"}>
              {criticalParams.length - missingParams.length}/{criticalParams.length} covered
            </Badge>
            {missingParams.length === 0 ? (
              <span className="text-sm text-green-600">✓ Calculator will work offline</span>
            ) : (
              <span className="text-sm text-red-600">✗ Missing critical fallbacks</span>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Critical Parameters with Fallbacks</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            {criticalParams.map(param => {
              const hasValue = param in FALLBACK_DEFAULTS;
              const value = hasValue ? FALLBACK_DEFAULTS[param as keyof typeof FALLBACK_DEFAULTS] : 'Missing';
              
              return (
                <div key={param} className={`flex justify-between p-2 rounded ${hasValue ? 'bg-green-50' : 'bg-red-50'}`}>
                  <span className="font-mono text-xs">{param}</span>
                  <span className={hasValue ? 'text-green-700' : 'text-red-700'}>
                    {hasValue ? value : 'Missing'}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {missingParams.length > 0 && (
          <div className="p-3 bg-red-50 rounded border border-red-200">
            <h4 className="font-medium text-red-800 mb-1">Missing Fallbacks:</h4>
            <ul className="text-sm text-red-700">
              {missingParams.map(param => (
                <li key={param} className="font-mono">• {param}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="text-xs text-gray-500 p-3 bg-gray-50 rounded">
          <strong>Test Instructions:</strong><br/>
          1. Disconnect your database connection<br/>
          2. Navigate to the calculator<br/>
          3. Verify all inputs show reasonable default values<br/>
          4. Confirm calculations produce sensible results
        </div>
      </CardContent>
    </Card>
  );
} 