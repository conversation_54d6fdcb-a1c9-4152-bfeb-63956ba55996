import { useEffect } from 'react';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { Button } from '@/components/ui/button';
import FocusedParameterManager from '@/components/admin/FocusedParameterManager';
import { Settings, LogOut, Calculator } from 'lucide-react';

export default function Admin() {
  const { user, isAdmin, isLoading, signOut } = useAdminAuth();

  useEffect(() => {
    if (!isLoading && !isAdmin) {
      // Force redirect with replace to prevent back button access
      window.location.replace('/admin-login');
    }
  }, [isLoading, isAdmin]);

  const handleSignOut = async () => {
    // signOut now handles navigation and cleanup internally
    await signOut();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-robot-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Settings className="h-6 w-6 text-robot-600" />
                <h1 className="text-xl font-semibold">Admin Panel</h1>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/'}
                className="flex items-center gap-2"
              >
                <Calculator className="h-4 w-4" />
                View Calculator
              </Button>
              
              <div className="text-sm text-gray-600">
                {user?.email}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        <FocusedParameterManager />
      </main>
    </div>
  );
} 