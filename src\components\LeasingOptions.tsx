import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn, formatCurrency } from "@/lib/utils";
import { useBusinessParameters } from "@/hooks/useBusinessParameters";

interface LeasingOptionsProps {
  selectedOption: string;
  onChange: (optionId: string) => void;
}

const LeasingOptions: React.FC<LeasingOptionsProps> = ({
  selectedOption,
  onChange,
}) => {
  const [hoveredOption, setHoveredOption] = useState<string | null>(null);
  const { getLeasingRates, getLeasingDurations } = useBusinessParameters();
  
  // Get leasing options from business parameters
  const leasingDurations = getLeasingDurations();
  const leasingRates = getLeasingRates();

  return (
    <div className="space-y-3">
      <Label className="font-medium">Leasing-Option wählen</Label>
      <RadioGroup
        value={selectedOption}
        onValueChange={onChange}
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        {leasingDurations.map((option: any) => {
          const isSelected = selectedOption === option.id;
          const isHovered = hoveredOption === option.id;
          const rate = leasingRates[option.id] || 0;

          return (
            <div key={option.id} className="relative">
              <RadioGroupItem
                value={option.id}
                id={option.id}
                className="sr-only"
              />
              <Label
                htmlFor={option.id}
                className={cn(
                  "flex flex-col p-4 border rounded-md cursor-pointer transition-all",
                  isHovered || isSelected
                    ? "bg-robot-50 shadow-md border-robot-500"
                    : option.isPopular ? "border-robot-300" : "border-gray-300"
                )}
                onMouseEnter={() => setHoveredOption(option.id)}
                onMouseLeave={() => setHoveredOption(null)}
              >
                {option.isPopular && (
                  <span className="absolute -top-2 right-2 text-xs font-medium bg-robot-500 text-white px-2 py-0.5 rounded-full">
                    Beliebt
                  </span>
                )}
                <span className="text-lg font-bold">{option.months} Monate</span>
                <span className="text-2xl font-bold text-robot-600 mt-1">
                  {formatCurrency(rate)}
                  <span className="text-sm font-normal text-gray-500">/Monat</span>
                </span>
              </Label>
            </div>
          );
        })}
      </RadioGroup>
    </div>
  );
};

export default LeasingOptions;
