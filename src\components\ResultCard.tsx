
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { formatCurrency } from "@/lib/utils";
import { TrendingUp, Clock, Calendar, Bot, User } from "lucide-react";

interface ResultCardProps {
  manualCost: number;
  robotCost: number;
  savings: number;
  monthlyCleanings: number;
  cleaningHours: number;
  showDetails?: boolean;
}

const ResultCard: React.FC<ResultCardProps> = ({
  manualCost,
  robotCost,
  savings,
  monthlyCleanings,
  cleaningHours,
  showDetails = true,
}) => {
  const yearlyManualCost = manualCost * 12;
  const yearlyRobotCost = robotCost * 12;
  const yearlySavings = savings * 12;
  const yearlyHours = cleaningHours * 12;
  const savingsPercentage = ((savings / manualCost) * 100).toFixed(0);

  return (
    <div className="space-y-6">
      {/* Main Savings Card */}
      <Card className="border-robot-200 shadow-lg bg-gradient-to-br from-white to-robot-50/30">
        <CardHeader className="pb-2">
          <CardTitle className="text-2xl font-bold text-center text-robot-800">
            ROI-Analyse
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center space-x-4 mb-6">
            <div className="text-center">
              <div className="text-5xl font-bold text-green-600">
                {formatCurrency(savings)}
              </div>
              <p className="text-sm text-gray-600 mt-1">Monatliche Einsparungen</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="relative h-4 bg-gray-200 rounded-full overflow-hidden mb-4">
            <div
              className="absolute h-full bg-gradient-to-r from-green-600 to-green-400 transition-all duration-500 rounded-full"
              style={{ width: `${Math.min(100, parseInt(savingsPercentage))}%` }}
            />
          </div>
          <p className="text-center text-sm text-gray-600 mb-6">
            {savingsPercentage}% Kostenreduzierung
          </p>

          <div className="grid grid-cols-2 gap-3 sm:gap-6">
            <div className="space-y-1 p-2 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-center gap-1 sm:gap-2">
                <User className="h-3 w-3 sm:h-4 sm:w-4 text-red-500" />
                <p className="text-xs sm:text-sm text-gray-500">Manuelle Kosten</p>
              </div>
              <p className="text-sm sm:text-xl font-bold text-red-500">
                {formatCurrency(manualCost)}
                <span className="text-[10px] sm:text-xs font-normal text-gray-500 ml-1">/Monat</span>
              </p>
            </div>
            <div className="space-y-1 p-2 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-center gap-1 sm:gap-2">
                <Bot className="h-3 w-3 sm:h-4 sm:w-4 text-robot-500" />
                <p className="text-xs sm:text-sm text-gray-500">Roboter-Kosten</p>
              </div>
              <p className="text-sm sm:text-xl font-bold text-robot-600">
                {formatCurrency(robotCost)}
                <span className="text-[10px] sm:text-xs font-normal text-gray-500 ml-1">/Monat</span>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Details Cards */}
      {showDetails && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-green-100">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Jährliche Gesamteinsparungen</p>
                  <p className="text-xl font-bold text-green-600">
                    {formatCurrency(yearlySavings)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-red-100">
                  <User className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Jährliche manuelle Kosten</p>
                  <p className="text-xl font-bold text-red-600">
                    {formatCurrency(yearlyManualCost)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-robot-100">
                  <Bot className="h-6 w-6 text-robot-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Jährliche Roboter-Kosten</p>
                  <p className="text-xl font-bold text-robot-600">
                    {formatCurrency(yearlyRobotCost)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ResultCard;
