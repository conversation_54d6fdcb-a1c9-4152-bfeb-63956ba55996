import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { LucideIcon } from "lucide-react";

interface NumberInputProps {
  id: string;
  label: string;
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
  icon?: LucideIcon;
  placeholder?: string;
  showSlider?: boolean;
}

const NumberInput: React.FC<NumberInputProps> = ({
  id,
  label,
  value,
  onChange,
  min = 0,
  max,
  step = 1,
  unit,
  icon: Icon,
  placeholder,
  showSlider = false,
}) => {
  const handleSliderChange = (newValue: number[]) => {
    console.log("NumberInput slider change:", newValue);
    const newVal = newValue[0];
    if (typeof newVal === 'number' && !isNaN(newVal)) {
      onChange(newVal);
    }
  };

  return (
    <div className="space-y-2 w-full">
      <Label htmlFor={id} className="font-medium">
        {label}
      </Label>
      <div className="relative">
        <Input
          id={id}
          type="number"
          value={value || ""}
          onChange={(e) => {
            const newValue = parseFloat(e.target.value);
            if (!isNaN(newValue)) {
              onChange(newValue);
            } else {
              onChange(0);
            }
          }}
          min={min}
          max={max}
          step={step}
          placeholder={placeholder}
          className={unit || Icon ? "pr-12" : ""}
        />
        {(unit || Icon) && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500">
            {unit || (Icon && <Icon className="h-5 w-5 text-gray-500" />)}
          </div>
        )}
      </div>
      {showSlider && (
        <div className="mt-6 space-y-3 py-2" style={{ marginTop: '1.5rem' }}>
          <div className="px-1">
            <Slider
              value={[Math.max(min, Math.min(value || min, max || 1000))]}
              min={min}
              max={max || 1000}
              step={step}
              onValueChange={handleSliderChange}
              className="w-full"
            />
          </div>
          <div className="flex justify-between px-1">
            <span className="text-sm text-gray-500">
              {min}
              {unit}
            </span>
            <span className="text-sm text-gray-500">
              {max}
              {unit}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default NumberInput;
